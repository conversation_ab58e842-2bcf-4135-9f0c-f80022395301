#!/usr/bin/env python3
"""
验证OCR优化功能配置
"""

import sys
import logging
from config import config
from ocr_processor import OCRProcessor
from main_controller import MainController


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )


def verify_config():
    """验证配置"""
    print("=" * 60)
    print("验证配置")
    print("=" * 60)
    
    try:
        # 获取系统配置
        system_config = config.get_system_config()
        
        print("✅ 系统配置:")
        print(f"   OCR基础路径: {system_config.get('ocr_base_path')}")
        print(f"   Docker容器: {system_config.get('docker_container')}")
        print(f"   OCR分片大小: {system_config.get('ocr_batch_size', '未配置')}")
        print(f"   OCR超时时间: {system_config.get('ocr_timeout_per_batch', '未配置')}秒")
        
        # 检查新配置项
        if 'ocr_batch_size' in system_config:
            print("✅ OCR分片大小配置正确")
        else:
            print("❌ OCR分片大小配置缺失")
            
        if 'ocr_timeout_per_batch' in system_config:
            print("✅ OCR超时时间配置正确")
        else:
            print("❌ OCR超时时间配置缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False


def verify_ocr_processor():
    """验证OCR处理器"""
    print("\n" + "=" * 60)
    print("验证OCR处理器")
    print("=" * 60)
    
    try:
        processor = OCRProcessor()
        
        print("✅ OCR处理器初始化成功")
        print(f"   分片大小: {processor.ocr_batch_size}")
        print(f"   超时时间: {processor.ocr_timeout_per_batch}秒")
        print(f"   案件关系表: {processor.case_relation_table}")
        print(f"   OCR基础路径: {processor.ocr_base_path}")
        print(f"   Docker容器: {processor.docker_container}")
        
        # 检查新方法是否存在
        methods_to_check = [
            'get_cases_for_ocr',
            'create_batch_chunks',
            'prepare_chunk_input_directory',
            'process_single_chunk',
            'process_batch_with_chunks',
            'update_chunk_failed_cases'
        ]
        
        for method_name in methods_to_check:
            if hasattr(processor, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR处理器验证失败: {e}")
        return False


def verify_main_controller():
    """验证主控制器"""
    print("\n" + "=" * 60)
    print("验证主控制器")
    print("=" * 60)
    
    try:
        controller = MainController()
        
        print("✅ 主控制器初始化成功")
        
        # 检查新方法是否存在
        methods_to_check = [
            'process_ocr_and_extraction_optimized',
            'process_chunk_extraction'
        ]
        
        for method_name in methods_to_check:
            if hasattr(controller, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 主控制器验证失败: {e}")
        return False


def verify_database_connection():
    """验证数据库连接"""
    print("\n" + "=" * 60)
    print("验证数据库连接")
    print("=" * 60)
    
    try:
        processor = OCRProcessor()
        
        with processor.get_connection() as connection:
            with connection.cursor() as cursor:
                # 检查表是否存在
                cursor.execute(f"SHOW TABLES LIKE '{processor.case_relation_table}'")
                result = cursor.fetchone()
                
                if result:
                    print(f"✅ 表 {processor.case_relation_table} 存在")
                    
                    # 检查表结构
                    cursor.execute(f"DESCRIBE {processor.case_relation_table}")
                    columns = cursor.fetchall()
                    
                    required_columns = ['batchid', 'ajbh', 'status', 'ajnr', 'error', 'updatetime']
                    existing_columns = [col[0] for col in columns]
                    
                    for col in required_columns:
                        if col in existing_columns:
                            print(f"✅ 字段 {col} 存在")
                        else:
                            print(f"❌ 字段 {col} 缺失")
                else:
                    print(f"❌ 表 {processor.case_relation_table} 不存在")
                    return False
        
        print("✅ 数据库连接正常")
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接验证失败: {e}")
        return False


def main():
    """主函数"""
    setup_logging()
    
    print("OCR优化功能验证")
    print("=" * 60)
    
    all_passed = True
    
    # 验证配置
    if not verify_config():
        all_passed = False
    
    # 验证OCR处理器
    if not verify_ocr_processor():
        all_passed = False
    
    # 验证主控制器
    if not verify_main_controller():
        all_passed = False
    
    # 验证数据库连接
    if not verify_database_connection():
        all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有验证通过！OCR优化功能配置正确。")
        print("\n可以使用以下命令测试功能:")
        print("  python test_optimized_ocr.py 1  # 测试OCR处理器")
        print("  python test_optimized_ocr.py 2  # 测试主控制器")
        print("  python test_optimized_ocr.py 3  # 测试完整流程")
    else:
        print("❌ 验证失败！请检查上述错误并修复。")
        sys.exit(1)


if __name__ == "__main__":
    main()
