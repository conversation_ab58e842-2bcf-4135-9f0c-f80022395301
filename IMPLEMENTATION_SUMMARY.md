# OCR分片批处理实施总结

## 问题背景

当案件数量超过600+时，OCR识别出现超时错误："OCR处理超时: 2025080815403901"。虽然实际上OCR已经完成了很多PDF的识别，但因为没有整批完成，导致ds_case_relation表的status全部都为4（报错状态），无法进行下一步处理。

## 解决方案

实施了**分片批处理**功能，将原本按整个批次处理的方式改为将一个批次分为多个片批来处理。

## 核心实现

### 1. 配置文件修改 (config.py)

**新增配置项：**
```python
# OCR分片处理配置
'ocr_batch_size': 20,  # OCR分片批处理大小，默认20个案件为一个分片
'ocr_timeout_per_batch': 1800,  # 每个分片的超时时间(秒)，默认30分钟
```

### 2. OCR处理器优化 (ocr_processor.py)

**新增核心方法：**

#### 分片管理
- `get_cases_for_ocr(batch_id)` - 获取需要OCR处理的案件列表
- `create_batch_chunks(cases)` - 将案件列表分片
- `prepare_chunk_input_directory(batch_id, ppid)` - 准备分片目录

#### 分片处理
- `process_single_chunk(batch_id, ppid, chunk_cases)` - 处理单个分片
- `process_batch_with_chunks(batch_id)` - 分片批处理主方法

#### 数据库操作
- `update_chunk_database(batch_id, ppid, ocr_results, chunk_cases)` - 更新分片数据库
- `update_chunk_failed_cases(batch_id, chunk_cases, error_message)` - 更新失败案件

**修改的方法：**
- `setup_ocr_directories(batch_id, ppid=None)` - 支持分片目录
- `run_ocr_processing(batch_id, ppid=None)` - 支持分片处理
- `read_ocr_results(batch_id, ppid=None)` - 支持分片结果读取

### 3. 主控制器优化 (main_controller.py)

**新增方法：**
- `process_ocr_and_extraction_optimized(batch_id)` - 优化版OCR和案件要素提取
- `process_chunk_extraction(batch_id, ppid)` - 分片案件要素提取

**修改的方法：**
- `run_time_range_task()` - 使用分片OCR处理

## 目录结构

### 分片目录结构
```
{ocr_base_path}/{batch_id}/{ppid}/input/   # 分片输入目录
{ocr_base_path}/{batch_id}/{ppid}/output/  # 分片输出目录
```

### Docker命令
```bash
# 分片模式
docker exec monkeyocr python parse.py ./data/{batch_id}/{ppid}/input -o ./data/{batch_id}/{ppid}/output
```

## 处理流程

### 优化前流程
```
数据获取 → PDF下载 → PDF合并 → OCR识别(整批，容易超时) → 案件要素提取(整批)
```

### 优化后流程
```
数据获取 → PDF下载 → PDF合并 → 分片OCR处理：
├── 创建分片（每片20个案件）
└── 按顺序处理每个分片：
    ├── 准备分片目录
    ├── 执行分片OCR识别
    ├── 读取分片OCR结果
    ├── 更新数据库状态（成功：status=2，失败：status=4）
    └── 立即进行该分片的案件要素提取
```

## 关键特性

### 1. 分片处理
- **分片大小**：默认20个案件/片，可配置
- **分片ID**：ppid，从1开始递增
- **独立处理**：每个分片独立处理，互不影响

### 2. 实时状态更新
- **OCR成功**：ajnr字段更新，status=2
- **OCR失败**：status=4，error字段记录错误
- **立即更新**：每个分片完成后立即更新数据库

### 3. 流水线处理
- **无需等待**：不需要等所有分片完成
- **立即处理**：OCR完成的分片立即进行案件要素提取
- **并行效率**：OCR和案件要素提取并行进行

## 使用方法

### 1. 测试功能
```bash
# 测试OCR分片处理
python test_chunk_ocr.py 1

# 测试优化版工作流程
python test_chunk_ocr.py 2

# 测试单个分片处理
python test_chunk_ocr.py 3
```

### 2. 正常使用
```bash
# 时间范围处理（自动使用分片OCR）
python main_controller.py 0 "2025-08-09 00:00:00" "2025-08-09 23:59:59"
```

## 性能提升

### 处理能力
- **优化前**：600+案件容易超时失败
- **优化后**：支持任意数量案件，按分片稳定处理

### 处理效率
- **优化前**：串行处理，OCR完成后才能进行案件要素提取
- **优化后**：流水线处理，OCR和案件要素提取并行进行

### 故障恢复
- **优化前**：整批失败需要重新开始
- **优化后**：只需重试失败的分片

## 监控方法

### 日志监控
```bash
# 查看处理日志
tail -f logs/main_controller_*.log | grep "分片"
```

### 数据库监控
```sql
-- 查看批次处理状态分布
SELECT status, COUNT(*) as count 
FROM ds_case_relation 
WHERE batchid = 'your_batch_id' 
GROUP BY status;

-- 查看处理进度
SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN status >= '2' THEN 1 ELSE 0 END) as ocr_completed,
    SUM(CASE WHEN status = '3' THEN 1 ELSE 0 END) as extraction_completed,
    SUM(CASE WHEN status = '4' THEN 1 ELSE 0 END) as failed
FROM ds_case_relation 
WHERE batchid = 'your_batch_id';
```

## 配置调优

### 根据服务器性能调整
```python
# 高性能服务器
'ocr_batch_size': 30,
'ocr_timeout_per_batch': 1200,  # 20分钟

# 低性能服务器
'ocr_batch_size': 10,
'ocr_timeout_per_batch': 2400,  # 40分钟
```

## 文件清单

### 修改的文件
- `config.py` - 新增分片配置
- `ocr_processor.py` - 实现分片处理逻辑
- `main_controller.py` - 实现优化版流程控制

### 新增的文件
- `test_chunk_ocr.py` - 功能测试脚本
- `CHUNK_OCR_GUIDE.md` - 详细使用指南
- `IMPLEMENTATION_SUMMARY.md` - 实施总结（本文件）

## 兼容性

- **向后兼容**：保留原有的处理方法
- **配置兼容**：新配置项有默认值
- **接口兼容**：主要接口保持不变

## 总结

通过实施分片批处理功能，成功解决了大批量案件OCR处理超时问题：

1. **解决超时**：600+案件不再出现整批超时
2. **提高效率**：流水线处理，OCR和案件要素提取并行
3. **增强稳定性**：单个分片失败不影响其他分片
4. **实时反馈**：每个分片完成后立即更新状态
5. **可配置性**：分片大小和超时时间可灵活调整

这个优化方案彻底解决了原有的超时问题，提供了更稳定、高效的大批量案件处理能力。
