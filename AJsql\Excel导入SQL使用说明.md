# Excel导入SQL工具使用说明

## 📋 功能概述

这个工具可以从Excel表格中提取指定字段，并将数据插入到MySQL数据库的 `djzs_db.ds_case_instrument_his_ai` 表中。

## 🎯 主要功能

- ✅ 从Excel中提取案件编号和数据版本号
- ✅ 将多个内容字段拼接成法律文书内容
- ✅ 自动生成PDF链接地址
- ✅ 批量插入数据到MySQL数据库
- ✅ 支持数据更新（重复键处理）
- ✅ 详细的日志记录

## 📦 依赖安装

```bash
pip install pandas openpyxl pymysql
```

## 🔧 配置说明

### 数据库配置
```python
db_config = {
    'host': '***********',
    'user': 'root',
    'password': '123456',
    'database': 'djzs_db',
    'charset': 'utf8mb4'
}
```

### 字段映射关系

| Excel字段 | MySQL字段 | 说明 |
|-----------|-----------|------|
| 案件编号 | ajbh | 案件编号 |
| 数据版本号 | xxzjbh | 信息主键编号 |
| 内容字段拼接 | flwsnr | 法律文书内容 |
| PDF链接 | flwslldz | 法律文书链路地址 |
| PDF链接 | flwsxzdz | 法律文书下载地址 |
| 当前时间 | ajlx | 案件类型 |
| 当前时间 | tbrksj | 同步入库时间 |
| 明天时间 | tbgxsj | 同步更新时间 |

### 内容字段配置
```python
content_fields = [
    '正文内容',
    '到案情况', 
    '依法侦查查明',
    '犯罪证据',
    '综上所述',
    '其他说明'
]
```

## 📊 Excel文件要求

### 必需字段
Excel文件必须包含以下字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 案件编号 | 唯一标识案件 | CASE001 |
| 数据版本号 | 数据版本标识 | V1.0 |
| 正文内容 | 案件正文 | 案件详细描述... |
| 到案情况 | 到案情况说明 | 嫌疑人已到案... |
| 依法侦查查明 | 侦查结果 | 经侦查查明... |
| 犯罪证据 | 证据材料 | 现有证据包括... |
| 综上所述 | 案件总结 | 综合以上情况... |
| 其他说明 | 补充说明 | 无其他说明 |

### Excel文件示例

| 案件编号 | 数据版本号 | 正文内容 | 到案情况 | 依法侦查查明 | 犯罪证据 | 综上所述 | 其他说明 |
|----------|------------|----------|----------|--------------|----------|----------|----------|
| CASE001 | V1.0 | 案件正文内容... | 嫌疑人已到案... | 侦查结果... | 证据材料... | 案件总结... | 补充说明... |
| CASE002 | V1.1 | 另一案件内容... | 嫌疑人在逃... | 继续侦查... | 证据收集中... | 进行中... | 需补充调查 |

## 🚀 使用方法

### 方法1: 命令行参数
```bash
python main_excel_to_sql.py "your_excel_file.xlsx"
```

### 方法2: 交互式输入
```bash
python main_excel_to_sql.py
```
然后按提示输入Excel文件路径。

### 方法3: 在代码中使用
```python
from main_excel_to_sql import ExcelToSQLImporter

# 创建导入器
importer = ExcelToSQLImporter()

# 执行导入
success = importer.import_excel_to_sql("data.xlsx")
```

## 📄 数据处理逻辑

### 1. 字段映射
- `案件编号` → `ajbh`
- `数据版本号` → `xxzjbh`

### 2. 内容拼接
将以下字段按格式拼接到 `flwsnr`：
```
正文内容: [内容]

到案情况: [内容]

依法侦查查明: [内容]

犯罪证据: [内容]

综上所述: [内容]

其他说明: [内容]
```

### 3. PDF链接生成
```
格式: 案件编号_数据版本号.pdf
示例: CASE001_V1.0.pdf

链接: http://***********:9999/api/v1/pdfHandler/getPdf?filename=CASE001_V1.0.pdf&action=view
```

### 4. 时间字段
- `ajlx`: 设置为 "AUTO_IMPORT"
- `tbrksj`: 当前时间
- `tbgxsj`: 明天时间
- `llsj`: 当前时间

### 5. 其他字段
- `flwszldm`: "02060403"
- `ajmc`: "案件_" + 案件编号

## 📝 日志文件

工具会生成详细的日志文件 `excel_to_sql.log`：

```
2025-08-07 18:30:00,123 - INFO - 开始读取Excel文件: data.xlsx
2025-08-07 18:30:00,456 - INFO - 成功读取Excel，共 100 行数据
2025-08-07 18:30:00,789 - INFO - 数据准备完成，共 100 条有效记录
2025-08-07 18:30:01,012 - INFO - 已插入 100 条记录
2025-08-07 18:30:01,345 - INFO - 数据插入完成！成功: 100, 失败: 0
```

## 🧪 测试功能

### 运行测试脚本
```bash
python test_excel_to_sql.py
```

### 测试内容
- ✅ 配置验证
- ✅ 数据库连接测试
- ✅ 导入功能测试
- ✅ 数据验证

## ⚠️ 注意事项

### 1. 数据库连接
- 确保数据库服务器可访问
- 确认用户权限足够
- 检查目标表是否存在

### 2. 数据处理
- 重复的案件编号会更新现有记录
- 空字段会被跳过
- 长文本会被正确处理

### 3. 错误处理
- 单条记录失败不会影响其他记录
- 详细错误信息记录在日志中
- 支持批量提交和回滚

### 4. 性能优化
- 每100条记录提交一次
- 批量插入提高效率
- 使用连接池管理数据库连接

## 🔧 故障排除

### 问题1: 数据库连接失败
**解决方案**:
- 检查数据库服务器是否运行
- 验证连接参数（主机、端口、用户名、密码）
- 确认网络连通性

### 问题2: Excel读取失败
**解决方案**:
- 确保Excel文件格式正确（.xlsx 或 .xls）
- 检查文件是否被其他程序占用
- 验证文件路径正确

### 问题3: 字段不匹配
**解决方案**:
- 检查Excel中的列名是否与配置一致
- 注意大小写和空格
- 可以修改配置文件中的字段映射

### 问题4: 插入失败
**解决方案**:
- 检查目标表结构是否正确
- 验证数据类型匹配
- 确认字段长度限制

## 🎯 高级用法

### 自定义配置
```python
# 修改 excel_to_sql_config.py
DATABASE_CONFIG = {
    'host': 'your_host',
    'user': 'your_user',
    'password': 'your_password',
    'database': 'your_database'
}
```

### 批量处理多个文件
```python
import os
from main_excel_to_sql import ExcelToSQLImporter

excel_dir = "excel_files"
importer = ExcelToSQLImporter()

for filename in os.listdir(excel_dir):
    if filename.endswith('.xlsx'):
        excel_path = os.path.join(excel_dir, filename)
        importer.import_excel_to_sql(excel_path)
```

### 数据验证
```python
# 导入后验证数据
with importer.get_connection() as connection:
    with connection.cursor() as cursor:
        cursor.execute("SELECT COUNT(*) FROM ds_case_instrument_his_ai WHERE ajlx = 'AUTO_IMPORT'")
        count = cursor.fetchone()[0]
        print(f"导入的记录数: {count}")
```

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件 `excel_to_sql.log`
2. 运行测试脚本验证环境
3. 检查数据库连接和表结构
4. 验证Excel文件格式和字段名

**工具已完成！** 🎉 现在你可以轻松地将Excel数据导入到MySQL数据库中。
