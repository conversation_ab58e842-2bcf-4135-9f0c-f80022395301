# OCR处理优化方案

## 问题描述

当案件数量非常多（例如600+条）时，OCR识别会出现超时错误："OCR处理超时: 2025080815403901"。虽然实际上OCR已经完成了很多PDF的识别，但因为没有整批完成，导致ds_case_relation表的status全部都为4（报错状态），因此也全部没有进行下一步处理。

## 优化方案

### 1. 分片批处理
- **分片大小配置**：默认每片20个案件，可在config.py中配置`ocr_batch_size`
- **分片目录结构**：
  ```
  /data/{batch_id}/{ppid}/input/   # 分片输入目录
  /data/{batch_id}/{ppid}/output/  # 分片输出目录
  ```
- **分片ID**：ppid，从1开始递增（1, 2, 3, 4...）

### 2. 并发控制
- **超时配置**：每个分片的超时时间可配置，默认30分钟
- **顺序处理**：按分片顺序进行OCR识别，避免资源冲突
- **实时状态更新**：每个分片完成后立即更新数据库状态

### 3. 流水线处理
- **分片完成即处理**：每个分片OCR完成后，立即进行案件要素提取
- **不等待全部完成**：无需等待所有分片完成，提高整体效率
- **状态实时更新**：
  - OCR成功：status = 2，ajnr字段更新
  - OCR失败：status = 4，error字段记录错误信息

## 配置说明

### config.py新增配置项

```python
# OCR分片处理配置
'ocr_batch_size': 20,  # OCR分片批处理大小，默认20个案件为一个分片
'ocr_timeout_per_batch': 1800,  # 每个分片的超时时间(秒)，默认30分钟
```

## 核心功能

### 1. OCRProcessor类新增方法

#### `get_cases_for_ocr(batch_id)`
- 获取需要OCR处理的案件列表（status=1）

#### `create_batch_chunks(cases)`
- 将案件列表按配置大小分片

#### `prepare_chunk_input_directory(batch_id, ppid, cases)`
- 为分片准备输入目录和PDF文件

#### `process_single_chunk(batch_id, ppid, chunk_cases)`
- 处理单个分片的OCR识别

#### `process_batch_with_chunks(batch_id)`
- 使用分片方式处理整个批次

#### `update_chunk_failed_cases(batch_id, chunk_cases, error_message)`
- 批量更新分片中失败案件的状态

### 2. MainController类新增方法

#### `process_ocr_and_extraction_optimized(batch_id)`
- 优化版OCR识别和案件要素提取
- 每个分片完成OCR后立即进行案件要素提取

#### `process_chunk_extraction(batch_id, ppid)`
- 处理分片的案件要素提取

## 处理流程

### 优化前流程
1. 数据获取 → 2. PDF下载 → 3. PDF合并 → 4. OCR识别（整批） → 5. 案件要素提取（整批）

### 优化后流程
1. 数据获取 → 2. PDF下载 → 3. PDF合并 → 4. 分片OCR处理：
   - 4.1 创建分片（每片20个案件）
   - 4.2 按顺序处理每个分片：
     - 4.2.1 准备分片输入目录
     - 4.2.2 执行分片OCR识别
     - 4.2.3 读取分片OCR结果
     - 4.2.4 更新数据库状态（成功：status=2，失败：status=4）
     - 4.2.5 立即进行该分片的案件要素提取

## 优势

1. **容错性强**：单个分片失败不影响其他分片
2. **实时反馈**：每个分片完成后立即更新状态
3. **资源优化**：避免大批量处理导致的资源耗尽
4. **流水线效率**：OCR和案件要素提取并行进行
5. **可配置性**：分片大小和超时时间可灵活配置

## 使用方法

### 1. 配置修改
在config.py中调整分片大小和超时时间：
```python
'ocr_batch_size': 20,  # 根据服务器性能调整
'ocr_timeout_per_batch': 1800,  # 根据案件复杂度调整
```

### 2. 运行优化版处理
```bash
# 使用优化版时间范围处理
python main_controller.py 0 "2025-01-03 09:00:00" "2025-01-04 09:00:00"
```

### 3. 测试功能
```bash
# 测试OCR处理器分片功能
python test_optimized_ocr.py 1

# 测试主控制器优化处理功能
python test_optimized_ocr.py 2

# 测试完整的优化工作流程
python test_optimized_ocr.py 3
```

## 监控和日志

### 日志输出示例
```
2025-08-09 10:00:00 - 开始分片OCR处理，批次: 2025080910000001
2025-08-09 10:00:01 - 创建了 30 个分片，每片最多 20 个案件
2025-08-09 10:00:02 - 开始处理分片 1，包含 20 个案件
2025-08-09 10:05:30 - 分片 1 OCR完成，成功: 18, 失败: 2
2025-08-09 10:05:31 - 开始分片 1 的案件要素提取
2025-08-09 10:08:45 - 分片 1 案件要素提取完成 - 成功: 16, 失败: 2
2025-08-09 10:08:46 - 分片 1 完整处理完成
2025-08-09 10:08:47 - 开始处理分片 2，包含 20 个案件
...
```

### 数据库状态监控
```sql
-- 查看批次处理状态
SELECT status, COUNT(*) as count 
FROM ds_case_relation 
WHERE batchid = '2025080910000001' 
GROUP BY status;

-- 查看处理进度
SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN status = '2' THEN 1 ELSE 0 END) as ocr_completed,
    SUM(CASE WHEN status = '3' THEN 1 ELSE 0 END) as extraction_completed,
    SUM(CASE WHEN status = '4' THEN 1 ELSE 0 END) as failed
FROM ds_case_relation 
WHERE batchid = '2025080910000001';
```

## 注意事项

1. **磁盘空间**：分片处理会创建更多临时目录，确保有足够磁盘空间
2. **Docker容器**：确保MonkeyOCR容器运行正常
3. **数据库连接**：分片处理会增加数据库连接频率，注意连接池配置
4. **日志文件**：分片处理会产生更多日志，注意日志文件大小管理
