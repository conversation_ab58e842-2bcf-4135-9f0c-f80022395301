#!/usr/bin/env python3
"""
Excel导入SQL测试脚本
"""

import pandas as pd
import os
import sys
from datetime import datetime
import pymysql

def create_test_excel():
    """创建测试Excel文件"""
    
    print("📊 创建测试Excel文件...")
    
    # 测试数据
    test_data = [
        {
            '案件编号': 'TEST001',
            '数据版本号': 'V1.0',
            '正文内容': '这是测试案件的正文内容，包含案件的基本情况描述。',
            '到案情况': '嫌疑人已主动到案，配合调查。',
            '依法侦查查明': '经过详细侦查，查明相关事实。',
            '犯罪证据': '现有证据包括监控录像、证人证言等。',
            '综上所述': '事实清楚，证据确凿。',
            '其他说明': '无其他特殊说明。'
        },
        {
            '案件编号': 'TEST002',
            '数据版本号': 'V1.1',
            '正文内容': '第二个测试案件的内容描述。',
            '到案情况': '当事人正在联系中。',
            '依法侦查查明': '初步调查结果。',
            '犯罪证据': '证据收集中。',
            '综上所述': '案件进行中。',
            '其他说明': '需要进一步调查。'
        },
        {
            '案件编号': 'TEST003',
            '数据版本号': 'V2.0',
            '正文内容': '第三个测试案件的详细内容。',
            '到案情况': '双方当事人均已到案。',
            '依法侦查查明': '调查工作已完成。',
            '犯罪证据': '证据材料齐全。',
            '综上所述': '可以结案。',
            '其他说明': '案件处理完毕。'
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 保存为Excel文件
    test_excel_path = 'test_data.xlsx'
    df.to_excel(test_excel_path, index=False)
    
    print(f"✅ 测试Excel文件已创建: {test_excel_path}")
    print(f"   包含 {len(test_data)} 条测试记录")
    
    return test_excel_path

def test_database_connection():
    """测试数据库连接"""
    
    print(f"\n🔍 测试数据库连接")
    print("="*30)
    
    db_config = {
        'host': '***********',
        'user': 'root',
        'password': '123456',
        'database': 'djzs_db',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ 数据库连接成功")
            print(f"   MySQL版本: {version[0]}")
            
            # 检查目标表是否存在
            cursor.execute("SHOW TABLES LIKE 'ds_case_instrument_his_ai'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                print(f"✅ 目标表存在: ds_case_instrument_his_ai")
                
                # 检查表结构
                cursor.execute("DESCRIBE ds_case_instrument_his_ai")
                columns = cursor.fetchall()
                print(f"   表字段数: {len(columns)}")
                
                # 检查关键字段
                key_fields = ['ajbh', 'xxzjbh', 'flwsnr', 'flwslldz', 'flwsxzdz']
                existing_fields = [col[0] for col in columns]
                
                for field in key_fields:
                    if field in existing_fields:
                        print(f"   ✅ 字段存在: {field}")
                    else:
                        print(f"   ❌ 字段缺失: {field}")
                
            else:
                print(f"❌ 目标表不存在: ds_case_instrument_his_ai")
                return False
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_import_function():
    """测试导入功能"""
    
    print(f"\n🧪 测试导入功能")
    print("="*30)
    
    # 创建测试Excel文件
    excel_path = create_test_excel()
    
    try:
        # 导入主模块
        from main_excel_to_sql import ExcelToSQLImporter
        
        print(f"\n🔄 开始测试导入...")
        
        # 创建导入器
        importer = ExcelToSQLImporter()
        
        # 执行导入
        success = importer.import_excel_to_sql(excel_path)
        
        if success:
            print(f"✅ 导入测试成功")
            
            # 验证插入的数据
            try:
                with importer.get_connection() as connection:
                    with connection.cursor() as cursor:
                        cursor.execute("""
                            SELECT ajbh, xxzjbh, flwslldz 
                            FROM ds_case_instrument_his_ai 
                            WHERE ajbh LIKE 'TEST%' 
                            ORDER BY ajbh
                        """)
                        results = cursor.fetchall()
                        
                        print(f"✅ 验证插入数据:")
                        for result in results:
                            print(f"   案件编号: {result[0]}, 版本号: {result[1]}")
                            print(f"   PDF链接: {result[2]}")
                        
            except Exception as e:
                print(f"⚠️  数据验证失败: {e}")
        else:
            print(f"❌ 导入测试失败")
            
        return success
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

def test_config():
    """测试配置功能"""
    
    print(f"\n🔧 测试配置功能")
    print("="*30)
    
    try:
        from excel_to_sql_config import get_config, validate_config
        
        # 验证配置
        errors = validate_config()
        if errors:
            print(f"❌ 配置验证失败:")
            for error in errors:
                print(f"   - {error}")
            return False
        else:
            print(f"✅ 配置验证通过")
        
        # 显示配置
        config = get_config()
        print(f"\n📋 配置信息:")
        print(f"   数据库: {config['database']['host']}:{config['database']['database']}")
        print(f"   目标表: {config['target_table']['table']}")
        print(f"   内容字段数: {len(config['content_fields'])}")
        
        return True
        
    except ImportError:
        print(f"⚠️  配置文件不存在，将使用默认配置")
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    
    print(f"\n🧹 清理测试数据...")
    
    # 删除测试Excel文件
    test_files = ['test_data.xlsx']
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"   ✅ 已删除: {file_path}")
            except Exception as e:
                print(f"   ❌ 删除失败 {file_path}: {e}")
    
    # 询问是否删除测试数据库记录
    choice = input(f"\n是否删除数据库中的测试记录? (y/N): ").strip().lower()
    if choice == 'y':
        try:
            db_config = {
                'host': '***********',
                'user': 'root',
                'password': '123456',
                'database': 'djzs_db',
                'charset': 'utf8mb4'
            }
            
            connection = pymysql.connect(**db_config)
            with connection.cursor() as cursor:
                cursor.execute("DELETE FROM ds_case_instrument_his_ai WHERE ajbh LIKE 'TEST%'")
                deleted_count = cursor.rowcount
                connection.commit()
                print(f"   ✅ 已删除 {deleted_count} 条测试记录")
            
            connection.close()
            
        except Exception as e:
            print(f"   ❌ 删除测试记录失败: {e}")

def main():
    """主函数"""
    
    print("🧪 Excel导入SQL工具测试")
    print("="*60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行测试
    tests = [
        ("配置功能", test_config),
        ("数据库连接", test_database_connection),
        ("导入功能", test_import_function)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"测试项目: {test_name}")
        print("="*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print(f"\n🎉 所有测试通过！Excel导入SQL工具可以正常使用")
        print(f"\n🚀 使用方法:")
        print(f"   python main_excel_to_sql.py your_excel_file.xlsx")
    else:
        print(f"\n⚠️  部分测试失败，请检查配置和数据库连接")
    
    # 清理测试数据
    cleanup_test_data()
    
    return passed == total

if __name__ == "__main__":
    main()
