#!/usr/bin/env python3
"""
调试SVG响应内容
检查Live Editor返回的SVG是否真的无效
"""

import requests
import base64

def debug_svg_response():
    """调试SVG响应内容"""
    
    print("🔍 调试Live Editor SVG响应")
    print("="*60)
    
    live_editor_url = "http://*************:8000"
    
    # 测试代码
    test_code = """
graph TD
    A[开始] --> B[处理]
    B --> C[结束]
    """
    
    print(f"测试代码:\n{test_code}")
    print(f"代码长度: {len(test_code.strip())} 字符")
    
    # 1. 测试 /api/render
    print(f"\n1. 测试 /api/render...")
    try:
        render_payload = {
            "code": test_code.strip(),
            "mermaid": {"theme": "default"}
        }
        
        response = requests.post(
            f"{live_editor_url}/api/render",
            json=render_payload,
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)}")
        print(f"响应前200字符: {response.text[:200]}")
        
        if "<svg" in response.text and "</svg>" in response.text:
            print("✅ 包含有效的SVG标签")
        else:
            print("❌ 不包含有效的SVG标签")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 2. 测试 /api/svg
    print(f"\n2. 测试 /api/svg...")
    try:
        svg_payload = {
            "code": test_code.strip(),
            "mermaid": {"theme": "default"}
        }
        
        response = requests.post(
            f"{live_editor_url}/api/svg",
            json=svg_payload,
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)}")
        print(f"响应前200字符: {response.text[:200]}")
        
        if "<svg" in response.text and "</svg>" in response.text:
            print("✅ 包含有效的SVG标签")
        else:
            print("❌ 不包含有效的SVG标签")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 3. 测试 GET /svg/<base64>
    print(f"\n3. 测试 GET /svg/<base64>...")
    try:
        encoded_code = base64.b64encode(test_code.strip().encode('utf-8')).decode('utf-8')
        validation_url = f"{live_editor_url}/svg/{encoded_code}"
        
        response = requests.get(
            validation_url,
            timeout=10,
            headers={'Accept': 'image/svg+xml'}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)}")
        print(f"响应前200字符: {response.text[:200]}")
        print(f"响应后200字符: {response.text[-200:]}")
        
        if "<svg" in response.text and "</svg>" in response.text:
            print("✅ 包含有效的SVG标签")
            
            # 更详细的检查
            svg_start = response.text.find("<svg")
            svg_end = response.text.find("</svg>") + 6
            
            if svg_start >= 0 and svg_end > svg_start:
                svg_content = response.text[svg_start:svg_end]
                print(f"✅ SVG内容长度: {len(svg_content)}")
                print(f"SVG开始: {svg_content[:100]}...")
                print(f"SVG结束: ...{svg_content[-100:]}")
                
                # 检查是否包含实际的图形内容
                if any(tag in svg_content for tag in ['<path', '<rect', '<circle', '<line', '<polygon']):
                    print("✅ 包含图形元素")
                else:
                    print("⚠️  可能不包含图形元素")
                    
                return True, svg_content
            else:
                print("❌ SVG标签结构异常")
        else:
            print("❌ 不包含有效的SVG标签")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return False, None

def test_improved_validation():
    """测试改进的验证逻辑"""
    
    print(f"\n{'='*60}")
    print("测试改进的验证逻辑")
    print("="*60)
    
    success, svg_content = debug_svg_response()
    
    if success and svg_content:
        print(f"\n✅ 找到有效的SVG内容")
        print("建议修改验证逻辑，接受这种格式的SVG")
        
        # 建议的新验证逻辑
        def improved_svg_check(svg_text):
            """改进的SVG检查逻辑"""
            if not svg_text or len(svg_text) < 50:
                return False
            
            # 检查基本SVG结构
            if "<svg" not in svg_text or "</svg>" not in svg_text:
                return False
            
            # 检查是否有实际内容（不只是空的SVG）
            svg_start = svg_text.find("<svg")
            svg_end = svg_text.find("</svg>") + 6
            
            if svg_start >= 0 and svg_end > svg_start:
                svg_content = svg_text[svg_start:svg_end]
                
                # 检查是否包含图形元素或文本
                has_graphics = any(tag in svg_content for tag in [
                    '<path', '<rect', '<circle', '<line', '<polygon', 
                    '<text', '<g', '<defs'
                ])
                
                # 或者检查是否有足够的内容长度
                has_content = len(svg_content) > 200
                
                return has_graphics or has_content
            
            return False
        
        # 测试新的验证逻辑
        is_valid = improved_svg_check(svg_content)
        print(f"改进的验证结果: {'✅ 有效' if is_valid else '❌ 无效'}")
        
        return is_valid
    else:
        print(f"\n❌ 没有找到有效的SVG内容")
        return False

def main():
    """主函数"""
    
    print("🧪 Live Editor SVG响应调试")
    print("检查为什么SVG被判断为无效")
    
    is_valid = test_improved_validation()
    
    print(f"\n{'='*60}")
    print("调试结论")
    print("="*60)
    
    if is_valid:
        print("✅ SVG响应实际上是有效的")
        print("问题在于验证逻辑过于严格")
        print("\n🔧 建议修复:")
        print("1. 修改case_extraction_agent.py中的SVG验证逻辑")
        print("2. 使用更宽松的SVG有效性检查")
        print("3. 重新测试hybrid模式")
    else:
        print("❌ SVG响应确实无效")
        print("需要检查Live Editor服务配置")
        print("\n🔧 建议:")
        print("1. 检查Live Editor服务是否正确配置")
        print("2. 尝试使用其他API端点")
        print("3. 考虑使用Docker验证作为主要方法")

if __name__ == "__main__":
    main()
