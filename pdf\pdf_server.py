#!/usr/bin/env python3
"""
简单的PDF文件HTTP服务器
提供PDF文件的在线查看和下载功能
"""

import os
import sys
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import mimetypes
import json

class PDFHandler(BaseHTTPRequestHandler):
    
    def __init__(self, *args, pdf_directory=None, **kwargs):
        self.pdf_directory = pdf_directory or os.getcwd()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        query_params = parse_qs(parsed_path.query)
        
        print(f"收到请求: {self.path}")
        
        if path == '/':
            # 主页 - 显示可用的PDF文件列表
            self.serve_index()
        elif path == '/api/v1/pdfHandler/getPdf':
            # PDF文件服务接口
            self.serve_pdf(query_params)
        elif path == '/api/v1/pdfHandler/listPdfs':
            # 列出所有PDF文件
            self.list_pdfs()
        else:
            self.send_error(404, "页面未找到")
    
    def serve_index(self):
        """提供主页"""
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>PDF文件服务器</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { max-width: 800px; margin: 0 auto; }
                .pdf-item { 
                    border: 1px solid #ddd; 
                    margin: 10px 0; 
                    padding: 15px; 
                    border-radius: 5px; 
                }
                .btn { 
                    background: #007cba; 
                    color: white; 
                    padding: 8px 16px; 
                    text-decoration: none; 
                    border-radius: 4px; 
                    margin-right: 10px;
                }
                .btn:hover { background: #005a87; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>PDF文件服务器</h1>
                <p>当前目录: {directory}</p>
                <div id="pdf-list">加载中...</div>
            </div>
            
            <script>
                // 加载PDF文件列表
                fetch('/api/v1/pdfHandler/listPdfs')
                    .then(response => response.json())
                    .then(data => {{
                        const listDiv = document.getElementById('pdf-list');
                        if (data.files.length === 0) {{
                            listDiv.innerHTML = '<p>未找到PDF文件</p>';
                            return;
                        }}
                        
                        let html = '<h2>可用的PDF文件:</h2>';
                        data.files.forEach(file => {{
                            const viewUrl = `/api/v1/pdfHandler/getPdf?filename=${{file.name}}&action=view`;
                            const downloadUrl = `/api/v1/pdfHandler/getPdf?filename=${{file.name}}&action=download`;
                            
                            html += `
                                <div class="pdf-item">
                                    <h3>${{file.name}}</h3>
                                    <p>大小: ${{file.size}} bytes | 修改时间: ${{file.modified}}</p>
                                    <a href="${{viewUrl}}" class="btn" target="_blank">在线查看</a>
                                    <a href="${{downloadUrl}}" class="btn">下载</a>
                                </div>
                            `;
                        }});
                        listDiv.innerHTML = html;
                    }})
                    .catch(error => {{
                        document.getElementById('pdf-list').innerHTML = '<p>加载失败: ' + error + '</p>';
                    }});
            </script>
        </body>
        </html>
        """.format(directory=self.pdf_directory)
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def list_pdfs(self):
        """列出所有PDF文件"""
        try:
            files = []
            for filename in os.listdir(self.pdf_directory):
                if filename.lower().endswith('.pdf'):
                    filepath = os.path.join(self.pdf_directory, filename)
                    stat = os.stat(filepath)
                    files.append({
                        'name': filename,
                        'size': stat.st_size,
                        'modified': os.path.getmtime(filepath)
                    })
            
            response = {'files': files}
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode('utf-8'))
            
        except Exception as e:
            self.send_error(500, f"服务器错误: {str(e)}")
    
    def serve_pdf(self, query_params):
        """提供PDF文件服务"""
        try:
            # 获取文件名参数
            filename = query_params.get('filename', [None])[0]
            action = query_params.get('action', ['view'])[0]
            
            if not filename:
                self.send_error(400, "缺少filename参数")
                return
            
            # 安全检查：防止路径遍历攻击
            if '..' in filename or '/' in filename or '\\' in filename:
                self.send_error(400, "非法文件名")
                return
            
            filepath = os.path.join(self.pdf_directory, filename)
            
            if not os.path.exists(filepath):
                self.send_error(404, f"文件未找到: {filename}")
                return
            
            if not filepath.lower().endswith('.pdf'):
                self.send_error(400, "只支持PDF文件")
                return
            
            # 读取文件
            with open(filepath, 'rb') as f:
                content = f.read()
            
            # 设置响应头
            self.send_response(200)
            self.send_header('Content-Type', 'application/pdf')
            self.send_header('Content-Length', str(len(content)))
            
            if action == 'download':
                # 下载模式
                self.send_header('Content-Disposition', f'attachment; filename="{filename}"')
            else:
                # 在线查看模式
                self.send_header('Content-Disposition', f'inline; filename="{filename}"')
            
            self.end_headers()
            self.wfile.write(content)
            
            print(f"成功提供文件: {filename} (动作: {action})")
            
        except Exception as e:
            print(f"服务PDF文件时出错: {str(e)}")
            self.send_error(500, f"服务器错误: {str(e)}")
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.date_time_string()}] {format % args}")

def create_handler(pdf_directory):
    """创建处理器工厂函数"""
    def handler(*args, **kwargs):
        return PDFHandler(*args, pdf_directory=pdf_directory, **kwargs)
    return handler

def main():
    # 配置
    HOST = '0.0.0.0'  # 监听所有网络接口
    PORT = 9999       # 端口号
    #PDF_DIR = '/bigai/ai/AJagent-main/pdf/pdf_output'  # PDF文件目录
    PDF_DIR = '/data/ai/AJagent-main/pdf'  # PDF文件目录
    # 检查目录是否存在
    if not os.path.exists(PDF_DIR):
        print(f"错误: 目录不存在 {PDF_DIR}")
        sys.exit(1)
    
    # 检查是否有test.pdf文件
    test_pdf = os.path.join(PDF_DIR, 'test.pdf')
    if os.path.exists(test_pdf):
        print(f"✅ 找到test.pdf文件: {test_pdf}")
    else:
        print(f"⚠️  未找到test.pdf文件: {test_pdf}")
    
    # 创建HTTP服务器
    handler = create_handler(PDF_DIR)
    server = HTTPServer((HOST, PORT), handler)
    
    print(f"🚀 PDF服务器启动成功!")
    print(f"📁 服务目录: {PDF_DIR}")
    print(f"🌐 服务地址: http://{HOST}:{PORT}")
    print(f"📄 PDF查看地址: http://{HOST}:{PORT}/api/v1/pdfHandler/getPdf?filename=test.pdf&action=view")
    print(f"⬇️  PDF下载地址: http://{HOST}:{PORT}/api/v1/pdfHandler/getPdf?filename=test.pdf&action=download")
    print(f"🏠 主页地址: http://{HOST}:{PORT}/")
    print("按 Ctrl+C 停止服务器")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        server.server_close()

if __name__ == '__main__':
    main()
