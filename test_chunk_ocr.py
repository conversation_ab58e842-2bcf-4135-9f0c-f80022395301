#!/usr/bin/env python3
"""
测试分片OCR处理功能
"""

import asyncio
import logging
import sys
from datetime import datetime
from ocr_processor import OCRProcessor
from main_controller import MainController


def setup_logging():
    """设置日志配置"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


async def test_ocr_chunk_processing():
    """测试OCR分片处理功能"""
    print("=" * 60)
    print("测试OCR分片处理功能")
    print("=" * 60)
    
    processor = OCRProcessor()
    
    # 测试批次
    batch_id = "test_batch_" + datetime.now().strftime('%Y%m%d%H%M%S')
    
    print(f"测试批次: {batch_id}")
    print(f"分片大小: {processor.ocr_batch_size}")
    print(f"超时时间: {processor.ocr_timeout_per_batch}秒")
    
    # 测试获取案件列表
    print("\n1. 测试获取案件列表...")
    cases = processor.get_cases_for_ocr(batch_id)
    print(f"获取到 {len(cases)} 个案件")
    
    if cases:
        # 测试创建分片
        print("\n2. 测试创建分片...")
        chunks = processor.create_batch_chunks(cases)
        print(f"创建了 {len(chunks)} 个分片")
        
        for ppid, chunk_cases in chunks:
            print(f"  分片 {ppid}: {len(chunk_cases)} 个案件")
            for case in chunk_cases[:3]:  # 只显示前3个
                print(f"    - {case['ajbh']}: {case['ajmc']}")
            if len(chunk_cases) > 3:
                print(f"    ... 还有 {len(chunk_cases) - 3} 个案件")
        
        # 测试分片批处理
        print("\n3. 测试分片批处理...")
        result = await processor.process_batch_with_chunks(batch_id)
        
        print(f"处理结果: {result['status']}")
        if result['status'] == 'success':
            print(f"总分片数: {result.get('total_chunks', 0)}")
            print(f"成功案件: {result.get('successful_count', 0)}")
            print(f"失败案件: {result.get('failed_count', 0)}")
        else:
            print(f"处理失败: {result.get('error')}")
    
    print("\n测试完成!")


async def test_optimized_workflow():
    """测试优化版工作流程"""
    print("=" * 60)
    print("测试优化版工作流程")
    print("=" * 60)
    
    controller = MainController()
    
    # 测试批次
    batch_id = "test_batch_" + datetime.now().strftime('%Y%m%d%H%M%S')
    
    print(f"测试批次: {batch_id}")
    
    # 测试优化处理方法
    print("\n1. 测试优化版OCR和案件要素提取...")
    result = await controller.process_ocr_and_extraction_optimized(batch_id)
    
    print(f"处理结果: {result['status']}")
    if result['status'] == 'success':
        print(f"总分片数: {result.get('total_chunks', 0)}")
        print(f"OCR成功: {result.get('ocr_successful_count', 0)}")
        print(f"OCR失败: {result.get('ocr_failed_count', 0)}")
        print(f"案件要素提取成功: {result.get('extraction_successful_count', 0)}")
        print(f"案件要素提取失败: {result.get('extraction_failed_count', 0)}")
    else:
        print(f"处理失败: {result.get('error')}")
    
    print("\n测试完成!")


async def test_single_chunk():
    """测试单个分片处理"""
    print("=" * 60)
    print("测试单个分片处理")
    print("=" * 60)
    
    processor = OCRProcessor()
    
    # 测试批次
    batch_id = "test_batch_" + datetime.now().strftime('%Y%m%d%H%M%S')
    
    print(f"测试批次: {batch_id}")
    
    # 获取案件列表
    cases = processor.get_cases_for_ocr(batch_id)
    
    if cases:
        # 创建分片
        chunks = processor.create_batch_chunks(cases)
        
        if chunks:
            # 测试第一个分片
            ppid, chunk_cases = chunks[0]
            print(f"\n测试分片 {ppid}，包含 {len(chunk_cases)} 个案件")
            
            # 准备分片目录
            print("1. 准备分片目录...")
            if processor.prepare_chunk_input_directory(batch_id, ppid):
                print("✅ 分片目录准备成功")
            else:
                print("❌ 分片目录准备失败")
                return
            
            # 处理单个分片
            print("2. 处理单个分片...")
            result = processor.process_single_chunk(batch_id, ppid, chunk_cases)
            
            print(f"处理结果: {result['status']}")
            if result['status'] == 'success':
                print(f"成功: {result.get('successful_count', 0)}")
                print(f"失败: {result.get('failed_count', 0)}")
            else:
                print(f"错误: {result.get('error')}")
        else:
            print("没有创建分片")
    else:
        print("没有找到需要处理的案件")
    
    print("\n测试完成!")


def print_usage():
    """打印使用说明"""
    print("使用方法:")
    print("  python test_chunk_ocr.py 1  # 测试OCR分片处理功能")
    print("  python test_chunk_ocr.py 2  # 测试优化版工作流程")
    print("  python test_chunk_ocr.py 3  # 测试单个分片处理")


async def main():
    """主函数"""
    setup_logging()
    
    if len(sys.argv) != 2:
        print_usage()
        sys.exit(1)
    
    test_type = int(sys.argv[1])
    
    if test_type == 1:
        await test_ocr_chunk_processing()
    elif test_type == 2:
        await test_optimized_workflow()
    elif test_type == 3:
        await test_single_chunk()
    else:
        print("无效的测试类型")
        print_usage()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
