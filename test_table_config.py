#!/usr/bin/env python3
"""
测试表名配置化功能
验证所有表名是否正确配置化
"""

import os
import sys
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append('/data/ai/AJagent-main')

def test_config_import():
    """测试配置导入"""
    print("🧪 测试配置导入")
    print("="*50)
    
    try:
        from config import config
        
        # 测试数据库配置
        db_config = config.get_db_config()
        print(f"✅ 数据库配置: {db_config['host']}:{db_config['database']}")
        
        # 测试表名配置
        table_config = config.get_table_config()
        print(f"✅ 表名配置:")
        for key, value in table_config.items():
            print(f"   {key}: {value}")
        
        # 测试获取单个表名
        source_table = config.get_table_name('source_table')
        case_relation_table = config.get_table_name('case_relation_table')
        case_details_table = config.get_table_name('case_details_table')
        
        print(f"✅ 单个表名获取:")
        print(f"   源数据表: {source_table}")
        print(f"   案件关系表: {case_relation_table}")
        print(f"   案件详细信息表: {case_details_table}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置导入失败: {e}")
        return False

def test_data_fetcher():
    """测试DataFetcher类"""
    print(f"\n🧪 测试DataFetcher类")
    print("="*50)
    
    try:
        from data_fetcher import DataFetcher
        
        df = DataFetcher()
        
        # 检查表名配置
        print(f"✅ DataFetcher表名配置:")
        print(f"   源数据表: {df.source_table}")
        print(f"   案件关系表: {df.case_relation_table}")
        print(f"   案件详细信息表: {df.case_details_table}")
        
        # 验证表名是否正确
        expected_tables = {
            'source_table': 'ds_case_instrument_his_ai',
            'case_relation_table': 'ds_case_relation',
            'case_details_table': 'ds_case_details'
        }
        
        for attr, expected in expected_tables.items():
            actual = getattr(df, attr)
            if actual == expected:
                print(f"   ✅ {attr}: {actual}")
            else:
                print(f"   ❌ {attr}: 期望 {expected}, 实际 {actual}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ DataFetcher测试失败: {e}")
        return False

def test_web_case_viewer():
    """测试web_case_viewer模块"""
    print(f"\n🧪 测试web_case_viewer模块")
    print("="*50)
    
    try:
        sys.path.append('/data/ai/AJagent-main/html_main')
        from web_case_viewer import get_table_name, TABLE_CONFIG
        
        # 测试表名获取函数
        case_relation_table = get_table_name('case_relation_table')
        case_details_table = get_table_name('case_details_table')
        
        print(f"✅ web_case_viewer表名配置:")
        print(f"   案件关系表: {case_relation_table}")
        print(f"   案件详细信息表: {case_details_table}")
        
        # 验证配置是否正确
        if case_relation_table == 'ds_case_relation':
            print(f"   ✅ 案件关系表名正确")
        else:
            print(f"   ❌ 案件关系表名错误: {case_relation_table}")
            return False
            
        if case_details_table == 'ds_case_details':
            print(f"   ✅ 案件详细信息表名正确")
        else:
            print(f"   ❌ 案件详细信息表名错误: {case_details_table}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ web_case_viewer测试失败: {e}")
        return False

def test_ocr_processor():
    """测试OCRProcessor类"""
    print(f"\n🧪 测试OCRProcessor类")
    print("="*50)
    
    try:
        from ocr_processor import OCRProcessor
        
        ocr = OCRProcessor()
        
        # 检查表名配置
        print(f"✅ OCRProcessor表名配置:")
        print(f"   案件关系表: {ocr.case_relation_table}")
        
        # 验证表名是否正确
        if ocr.case_relation_table == 'ds_case_relation':
            print(f"   ✅ 案件关系表名正确")
        else:
            print(f"   ❌ 案件关系表名错误: {ocr.case_relation_table}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ OCRProcessor测试失败: {e}")
        return False

def test_case_extraction_agent():
    """测试CaseExtractionAgent类"""
    print(f"\n🧪 测试CaseExtractionAgent类")
    print("="*50)
    
    try:
        from case_extraction_agent import CaseExtractionAgent
        
        agent = CaseExtractionAgent()
        
        # 检查表名配置
        print(f"✅ CaseExtractionAgent表名配置:")
        print(f"   案件关系表: {agent.case_relation_table}")
        print(f"   案件详细信息表: {agent.case_details_table}")
        
        # 验证表名是否正确
        expected_tables = {
            'case_relation_table': 'ds_case_relation',
            'case_details_table': 'ds_case_details'
        }
        
        for attr, expected in expected_tables.items():
            actual = getattr(agent, attr)
            if actual == expected:
                print(f"   ✅ {attr}: {actual}")
            else:
                print(f"   ❌ {attr}: 期望 {expected}, 实际 {actual}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ CaseExtractionAgent测试失败: {e}")
        return False

def test_file_content():
    """测试文件内容是否包含配置化的表名"""
    print(f"\n🧪 测试文件内容")
    print("="*50)
    
    files_to_check = [
        'data_fetcher.py',
        'html_main/web_case_viewer.py',
        'html_main/diagnose_database.py',
        'ocr_processor.py',
        'case_extraction_agent.py'
    ]
    
    # 检查是否还有硬编码的表名
    hardcoded_patterns = [
        'FROM ds_case_relation',
        'UPDATE ds_case_relation',
        'INSERT INTO ds_case_relation',
        'FROM `ds_case_relation`',
        'UPDATE `ds_case_relation`',
        'INSERT INTO `ds_case_relation`',
        'FROM ds_case_details',
        'UPDATE ds_case_details',
        'INSERT INTO ds_case_details',
        'FROM `ds_case_details`',
        'UPDATE `ds_case_details`',
        'INSERT INTO `ds_case_details`'
    ]
    
    issues_found = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                file_issues = []
                for pattern in hardcoded_patterns:
                    if pattern in content:
                        file_issues.append(pattern)
                
                if file_issues:
                    issues_found.append((file_path, file_issues))
                    print(f"   ⚠️  {file_path}: 发现硬编码表名")
                    for issue in file_issues:
                        print(f"      - {issue}")
                else:
                    print(f"   ✅ {file_path}: 无硬编码表名")
                    
            except Exception as e:
                print(f"   ❌ {file_path}: 读取失败 - {e}")
        else:
            print(f"   ⚠️  {file_path}: 文件不存在")
    
    return len(issues_found) == 0

def main():
    """主函数"""
    
    print("🧪 表名配置化功能测试")
    print("="*70)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行所有测试
    tests = [
        ("配置导入", test_config_import),
        ("DataFetcher类", test_data_fetcher),
        ("web_case_viewer模块", test_web_case_viewer),
        ("OCRProcessor类", test_ocr_processor),
        ("CaseExtractionAgent类", test_case_extraction_agent),
        ("文件内容检查", test_file_content)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n{'='*70}")
    print("测试结果汇总")
    print("="*70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print(f"\n🎉 表名配置化功能测试全部通过！")
        print(f"✅ 所有表名已成功配置化")
        print(f"✅ 配置文件正常工作")
        print(f"✅ 各模块正确使用配置化表名")
        
        print(f"\n📋 配置化的表名:")
        print(f"   源数据表: ds_case_instrument_his_ai")
        print(f"   案件关系表: ds_case_relation")
        print(f"   案件详细信息表: ds_case_details")
        
        print(f"\n🔧 如需修改表名，请编辑 config.py 中的 table_config 配置")
        
    else:
        print(f"\n⚠️  部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    main()
