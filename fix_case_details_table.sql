-- 修复ds_case_details表结构
-- 问题：当前主键是(batchid, ajbh)，但一个案件可能有多个实体记录
-- 解决：添加自增ID作为主键，保留(batchid, ajbh)作为索引

USE `djzs_db`;

-- 1. 备份现有数据（可选）
-- CREATE TABLE `ds_case_details_backup` AS SELECT * FROM `ds_case_details`;

-- 2. 检查当前表结构
DESCRIBE `ds_case_details`;

-- 3. 删除现有的主键约束
ALTER TABLE `ds_case_details` DROP PRIMARY KEY;

-- 4. 添加自增ID列作为新的主键
ALTER TABLE `ds_case_details` 
ADD COLUMN `id` INT UNSIGNED NOT NULL AUTO_INCREMENT FIRST,
ADD PRIMARY KEY (`id`);

-- 5. 为原来的主键字段添加索引
ALTER TABLE `ds_case_details` 
ADD INDEX `idx_batchid_ajbh_unique` (`batchid`, `ajbh`);

-- 6. 验证表结构
DESCRIBE `ds_case_details`;

-- 7. 检查现有数据
SELECT COUNT(*) as total_records FROM `ds_case_details`;

-- 8. 显示修复后的表结构
SHOW CREATE TABLE `ds_case_details`;

-- 9. 测试插入（可选）
-- INSERT INTO `ds_case_details` 
-- (batchid, ajbh, ajmc, entity_type, name_code, data_time)
-- VALUES 
-- ('TEST_BATCH', 'TEST_CASE', '测试案件', '人员', '测试人员1', NOW()),
-- ('TEST_BATCH', 'TEST_CASE', '测试案件', '人员', '测试人员2', NOW());

-- 10. 清理测试数据（如果执行了测试插入）
-- DELETE FROM `ds_case_details` WHERE batchid = 'TEST_BATCH';

COMMIT;
