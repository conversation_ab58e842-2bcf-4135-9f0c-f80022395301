# Excel转PDF工具使用说明

## 📋 功能概述

这个工具可以从Excel表格中提取指定字段，将内容拼接后生成PDF文件。每个PDF文件以"案件编号_数据版本号.pdf"的格式命名。

## 🎯 主要功能

- ✅ 从Excel中提取指定字段数据
- ✅ 将多个内容字段拼接成完整文档
- ✅ 生成格式化的PDF文件
- ✅ 支持中文字体显示
- ✅ 自动处理文件名非法字符
- ✅ 详细的日志记录
- ✅ 可配置的字段和样式

## 📦 依赖安装

```bash
pip install pandas openpyxl reportlab
```

## 🔧 配置说明

### 默认字段配置

#### PDF文件名字段
```python
pdf_name_fields = ['案件编号', '数据版本号']
```

#### 内容字段
```python
content_fields = ['正文内容', '到案情况', '依法侦查查明', '犯罪证据', '综上所述', '其他说明']
```

### 自定义配置

可以通过修改 `excel_to_pdf_config.py` 文件来自定义配置：

```python
# 修改字段配置
EXCEL_CONFIG = {
    'pdf_name_fields': ['案件编号', '数据版本号'],
    'content_fields': ['正文内容', '到案情况', '依法侦查查明', '犯罪证据', '综上所述', '其他说明'],
    'field_display_names': {
        '正文内容': '案件正文',  # 可以修改显示名称
        '到案情况': '到案情况'
    }
}

# 修改PDF样式
PDF_CONFIG = {
    'fonts': {
        'title_size': 16,
        'section_size': 14,
        'body_size': 12
    },
    'styles': {
        'title_alignment': 1,  # 0=左对齐, 1=居中, 2=右对齐
        'add_generate_time': True
    }
}
```

## 🚀 使用方法

### 方法1: 命令行参数
```bash
python main_excel_to_pdf.py "path/to/your/excel.xlsx" "path/to/output/directory"
```

### 方法2: 交互式输入
```bash
python main_excel_to_pdf.py
```
然后按提示输入Excel文件路径和输出目录。

### 方法3: 在代码中使用
```python
from main_excel_to_pdf import ExcelToPDFConverter

# 创建转换器
converter = ExcelToPDFConverter(
    excel_path="data.xlsx",
    output_dir="./pdf_output"
)

# 执行转换
success = converter.convert_all()
```

## 📊 Excel文件要求

### 必需字段
Excel文件必须包含以下字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 案件编号 | 用于生成PDF文件名 | CASE001 |
| 数据版本号 | 用于生成PDF文件名 | V1.0 |
| 正文内容 | PDF内容字段 | 案件详细描述... |
| 到案情况 | PDF内容字段 | 嫌疑人到案情况... |
| 依法侦查查明 | PDF内容字段 | 侦查过程和结果... |
| 犯罪证据 | PDF内容字段 | 相关证据材料... |
| 综上所述 | PDF内容字段 | 案件总结... |
| 其他说明 | PDF内容字段 | 补充说明... |

### Excel文件示例

| 案件编号 | 数据版本号 | 正文内容 | 到案情况 | 依法侦查查明 | 犯罪证据 | 综上所述 | 其他说明 |
|----------|------------|----------|----------|--------------|----------|----------|----------|
| CASE001 | V1.0 | 这是案件的正文内容... | 嫌疑人已到案... | 经侦查查明... | 现有证据包括... | 综合以上情况... | 无其他说明 |
| CASE002 | V1.1 | 另一个案件的内容... | 嫌疑人在逃... | 继续侦查中... | 证据收集中... | 案件进行中... | 需要补充调查 |

## 📄 PDF输出格式

### 文件命名
- 格式：`案件编号_数据版本号.pdf`
- 示例：`CASE001_V1.0.pdf`

### PDF内容结构
```
案件编号: CASE001 | 数据版本号: V1.0

正文内容
这是案件的正文内容...

到案情况  
嫌疑人已到案...

依法侦查查明
经侦查查明...

犯罪证据
现有证据包括...

综上所述
综合以上情况...

其他说明
无其他说明

生成时间: 2025-08-07 15:30:00
```

## 📁 输出目录结构

```
pdf_output/
├── CASE001_V1.0.pdf
├── CASE002_V1.1.pdf
├── CASE003_V2.0.pdf
└── ...
```

## 📝 日志文件

工具会生成详细的日志文件 `excel_to_pdf.log`：

```
2025-08-07 15:30:00,123 - INFO - 开始读取Excel文件: data.xlsx
2025-08-07 15:30:00,456 - INFO - 成功读取Excel，共 10 行数据
2025-08-07 15:30:00,789 - INFO - 开始转换 10 条记录为PDF
2025-08-07 15:30:01,012 - INFO - 成功生成PDF: CASE001_V1.0.pdf
2025-08-07 15:30:01,345 - INFO - 成功生成PDF: CASE002_V1.1.pdf
2025-08-07 15:30:01,678 - INFO - 转换完成！成功: 10, 失败: 0
```

## ⚠️ 注意事项

### 1. 字体支持
- 工具会自动尝试注册中文字体
- 支持Windows、Linux、macOS系统的常见中文字体
- 如果没有中文字体，会使用默认字体（可能显示乱码）

### 2. 文件名处理
- 自动清理文件名中的非法字符（`< > : " / \ | ? *`）
- 非法字符会被替换为下划线 `_`

### 3. 数据处理
- 空字段会被跳过，不会在PDF中显示
- 换行符会被正确处理为PDF中的换行

### 4. 错误处理
- 如果某行数据缺少案件编号或数据版本号，会跳过该行
- 详细错误信息会记录在日志文件中

## 🔧 故障排除

### 问题1: 中文显示乱码
**解决方案**: 
- 确保系统安装了中文字体
- Windows: 确保有宋体、黑体等字体
- Linux: 安装中文字体包
- macOS: 系统自带中文字体

### 问题2: Excel读取失败
**解决方案**:
- 确保Excel文件格式正确（.xlsx 或 .xls）
- 检查文件是否被其他程序占用
- 确保文件路径正确

### 问题3: PDF生成失败
**解决方案**:
- 检查输出目录是否有写入权限
- 确保磁盘空间充足
- 检查文件名是否包含非法字符

### 问题4: 字段不匹配
**解决方案**:
- 检查Excel中的列名是否与配置中的字段名完全一致
- 注意大小写和空格
- 可以修改配置文件中的字段名

## 🎯 高级用法

### 批量处理多个Excel文件
```python
import os
from main_excel_to_pdf import ExcelToPDFConverter

excel_dir = "excel_files"
output_dir = "pdf_output"

for filename in os.listdir(excel_dir):
    if filename.endswith('.xlsx'):
        excel_path = os.path.join(excel_dir, filename)
        converter = ExcelToPDFConverter(excel_path, output_dir)
        converter.convert_all()
```

### 自定义PDF样式
```python
# 修改 excel_to_pdf_config.py 中的配置
PDF_CONFIG = {
    'fonts': {
        'title_size': 18,    # 更大的标题
        'section_size': 16,  # 更大的章节标题
        'body_size': 11      # 稍小的正文
    },
    'styles': {
        'title_alignment': 0,      # 左对齐标题
        'add_generate_time': False # 不显示生成时间
    }
}
```

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件 `excel_to_pdf.log`
2. 检查Excel文件格式和字段名
3. 确认系统字体支持
4. 验证文件路径和权限

**工具已完成！** 🎉 现在你可以轻松地将Excel数据转换为格式化的PDF文件。
