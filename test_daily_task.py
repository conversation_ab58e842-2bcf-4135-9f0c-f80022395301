#!/usr/bin/env python3
"""
测试每24小时定时任务（处理当天数据）
验证task_type=4的功能是否正确
"""

import sys
import os
import subprocess
import time
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append('/data/ai/AJagent-main')

def test_job_function():
    """测试job函数中的task_type=4逻辑"""
    
    print("🧪 测试job函数中的task_type=4逻辑")
    print("="*50)
    
    # 检查main_controller.py文件
    main_controller_path = "main_controller.py"
    
    if not os.path.exists(main_controller_path):
        print(f"❌ 文件不存在: {main_controller_path}")
        return False
    
    try:
        with open(main_controller_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查task_type=4的相关代码
        required_patterns = [
            "elif task_type == 4:",
            "处理当天的数据",
            "start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)",
            "end_time = now.replace(hour=23, minute=59, second=59, microsecond=999999)",
            "task_type in [1, 2, 3, 4]",
            "schedule.every().day.at(\"23:30\").do(job)"
        ]
        
        print("检查关键代码模式:")
        found_patterns = []
        missing_patterns = []
        
        for pattern in required_patterns:
            if pattern in content:
                found_patterns.append(pattern)
                print(f"   ✅ 找到: {pattern}")
            else:
                missing_patterns.append(pattern)
                print(f"   ❌ 缺失: {pattern}")
        
        if len(found_patterns) >= len(required_patterns) * 0.8:
            print(f"\n🎉 job函数逻辑检查通过!")
            print(f"   找到 {len(found_patterns)}/{len(required_patterns)} 个关键模式")
            return True
        else:
            print(f"\n⚠️  job函数逻辑可能不完整")
            print(f"   只找到 {len(found_patterns)}/{len(required_patterns)} 个关键模式")
            return False
            
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def test_start_py_menu():
    """测试start.py菜单更新"""
    
    print(f"\n🧪 测试start.py菜单更新")
    print("="*50)
    
    start_py_path = "start.py"
    
    if not os.path.exists(start_py_path):
        print(f"❌ 文件不存在: {start_py_path}")
        return False
    
    try:
        with open(start_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查菜单更新
        menu_patterns = [
            "7. 每24小时定时任务（每天23:30执行，处理当天数据）",
            "请输入选项编号 (1-10):",
            "elif choice == \"7\":",
            "handle_scheduled_task(4)",
            "4: \"每24小时定时任务（处理当天数据）\"",
            "4: \"每天23:30执行，处理当天的数据\""
        ]
        
        print("检查菜单更新:")
        found_count = 0
        for pattern in menu_patterns:
            if pattern in content:
                found_count += 1
                print(f"   ✅ 找到: {pattern}")
            else:
                print(f"   ❌ 缺失: {pattern}")
        
        if found_count >= len(menu_patterns) * 0.8:
            print(f"\n🎉 start.py菜单更新检查通过!")
            return True
        else:
            print(f"\n⚠️  start.py菜单更新可能不完整")
            return False
            
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def test_time_calculation():
    """测试时间计算逻辑"""
    
    print(f"\n🧪 测试时间计算逻辑")
    print("="*50)
    
    # 模拟当前时间
    now = datetime.now()
    
    print(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 模拟task_type=4的时间计算
    start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = now.replace(hour=23, minute=59, second=59, microsecond=999999)
    
    print(f"\ntask_type=4 时间范围计算:")
    print(f"   开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   时间跨度: {(end_time - start_time).total_seconds() / 3600:.1f} 小时")
    
    # 验证时间范围
    if start_time.hour == 0 and start_time.minute == 0 and start_time.second == 0:
        print(f"   ✅ 开始时间正确（当天00:00:00）")
    else:
        print(f"   ❌ 开始时间错误")
        return False
    
    if end_time.hour == 23 and end_time.minute == 59 and end_time.second == 59:
        print(f"   ✅ 结束时间正确（当天23:59:59）")
    else:
        print(f"   ❌ 结束时间错误")
        return False
    
    # 验证是同一天
    if start_time.date() == end_time.date() == now.date():
        print(f"   ✅ 时间范围为当天")
    else:
        print(f"   ❌ 时间范围不是当天")
        return False
    
    print(f"\n🎉 时间计算逻辑正确!")
    return True

def test_schedule_time():
    """测试定时执行时间"""
    
    print(f"\n🧪 测试定时执行时间")
    print("="*50)
    
    # 模拟不同的执行时间
    test_times = [
        datetime.now().replace(hour=23, minute=30, second=0),  # 23:30
        datetime.now().replace(hour=0, minute=0, second=0),    # 00:00
        datetime.now().replace(hour=12, minute=0, second=0),   # 12:00
    ]
    
    print("定时任务执行时间对比:")
    print("   task_type=3: 每天00:00执行，处理前一天数据")
    print("   task_type=4: 每天23:30执行，处理当天数据")
    
    for test_time in test_times:
        print(f"\n假设当前时间: {test_time.strftime('%H:%M:%S')}")
        
        if test_time.hour == 23 and test_time.minute == 30:
            print(f"   ✅ task_type=4 应该执行（处理当天数据）")
        elif test_time.hour == 0 and test_time.minute == 0:
            print(f"   ✅ task_type=3 应该执行（处理前一天数据）")
        else:
            print(f"   ⏸️  两个任务都不执行")
    
    print(f"\n🎯 任务特点:")
    print(f"   task_type=3: 适合日终处理，处理完整的前一天数据")
    print(f"   task_type=4: 适合当日汇总，处理当天累积的数据")
    
    return True

def test_auto_start_script():
    """测试自动启动脚本"""
    
    print(f"\n🧪 测试自动启动脚本")
    print("="*50)
    
    auto_script = "auto_start_daily.py"
    
    if os.path.exists(auto_script):
        print(f"✅ 自动启动脚本存在: {auto_script}")
        
        # 检查脚本内容
        try:
            with open(auto_script, 'r', encoding='utf-8') as f:
                content = f.read()
            
            script_patterns = [
                "main_controller.py', '4'",
                "每24小时定时任务（处理当天数据）",
                "每天23:30执行",
                "处理当天00:00-23:59的数据"
            ]
            
            print("检查脚本内容:")
            for pattern in script_patterns:
                if pattern in content:
                    print(f"   ✅ 包含: {pattern}")
                else:
                    print(f"   ❌ 缺失: {pattern}")
            
            print(f"\n📋 使用方法:")
            print(f"   python {auto_script}")
            
        except Exception as e:
            print(f"❌ 读取脚本失败: {e}")
            return False
    else:
        print(f"❌ 自动启动脚本不存在: {auto_script}")
        return False
    
    return True

def main():
    """主函数"""
    
    print("🧪 每24小时定时任务（处理当天数据）测试")
    print("="*70)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试1: job函数逻辑
    test1_result = test_job_function()
    
    # 测试2: start.py菜单
    test2_result = test_start_py_menu()
    
    # 测试3: 时间计算
    test3_result = test_time_calculation()
    
    # 测试4: 定时执行时间
    test4_result = test_schedule_time()
    
    # 测试5: 自动启动脚本
    test5_result = test_auto_start_script()
    
    print(f"\n{'='*70}")
    print("测试结果汇总")
    print("="*70)
    
    print(f"job函数逻辑检查: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"start.py菜单更新: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"时间计算逻辑: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"定时执行时间: {'✅ 通过' if test4_result else '❌ 失败'}")
    print(f"自动启动脚本: {'✅ 通过' if test5_result else '❌ 失败'}")
    
    if all([test1_result, test2_result, test3_result, test4_result, test5_result]):
        print(f"\n🎉 每24小时定时任务（处理当天数据）测试通过！")
        print(f"✅ task_type=4 逻辑已正确实现")
        print(f"✅ 菜单选项已正确更新")
        print(f"✅ 时间计算逻辑正确")
        print(f"✅ 定时执行配置正确")
        
        print(f"\n🚀 使用方法:")
        print(f"1. 交互式启动: python start.py (选择选项7)")
        print(f"2. 直接启动: python main_controller.py 4")
        print(f"3. 后台启动: python auto_start_daily.py")
        
        print(f"\n📊 任务特点:")
        print(f"   执行时间: 每天23:30")
        print(f"   处理数据: 当天00:00-23:59")
        print(f"   立即执行: 启动时立即处理一次")
        
    else:
        print(f"\n⚠️  部分测试失败，需要进一步检查")
    
    return all([test1_result, test2_result, test3_result, test4_result, test5_result])

if __name__ == "__main__":
    main()
