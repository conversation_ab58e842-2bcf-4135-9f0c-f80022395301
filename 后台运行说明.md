# 后台运行说明

## 🚀 快速启动（推荐）

### 方法1: 使用自动化脚本
```bash
# 自动启动每小时定时任务
python auto_start_hourly.py
```

### 方法2: 使用Shell脚本
```bash
# 设置执行权限
chmod +x *.sh

# 启动后台服务
./start_background.sh
```

## 📋 管理命令

### 启动服务
```bash
./start_background.sh
```

### 停止服务
```bash
./stop_background.sh
```

### 重启服务
```bash
./restart_background.sh
```

### 查看状态
```bash
./status_background.sh
```

### 查看日志
```bash
# 查看最新日志
tail -f logs/system_*.log

# 查看特定时间的日志
ls logs/system_*.log
tail -f logs/system_20250805_172930.log
```

## 🔧 手动后台运行

### 使用nohup
```bash
# 后台运行start.py
nohup python start.py > logs/system.log 2>&1 &

# 查看进程ID
echo $!

# 保存进程ID到文件
echo $! > logs/system.pid
```

### 使用screen
```bash
# 创建新的screen会话
screen -S case-processor

# 在screen中运行
python start.py

# 分离screen会话 (Ctrl+A, D)
# 重新连接: screen -r case-processor
```

### 使用tmux
```bash
# 创建新的tmux会话
tmux new-session -d -s case-processor

# 在tmux中运行命令
tmux send-keys -t case-processor 'cd /data/ai/AJagent-main' Enter
tmux send-keys -t case-processor 'python start.py' Enter

# 查看会话: tmux list-sessions
# 连接会话: tmux attach-session -t case-processor
```

## 🏭 生产环境部署（systemd）

### 1. 安装服务文件
```bash
# 复制服务文件
sudo cp case-processor.service /etc/systemd/system/

# 重新加载systemd
sudo systemctl daemon-reload

# 启用服务（开机自启）
sudo systemctl enable case-processor

# 启动服务
sudo systemctl start case-processor
```

### 2. 管理systemd服务
```bash
# 查看状态
sudo systemctl status case-processor

# 启动服务
sudo systemctl start case-processor

# 停止服务
sudo systemctl stop case-processor

# 重启服务
sudo systemctl restart case-processor

# 查看日志
sudo journalctl -u case-processor -f

# 查看最近的日志
sudo journalctl -u case-processor --since "1 hour ago"
```

## 📊 监控和维护

### 进程监控
```bash
# 查看Python进程
ps aux | grep python | grep start.py

# 查看特定PID
ps aux | grep $(cat logs/system.pid)

# 查看进程树
pstree -p $(cat logs/system.pid)
```

### 资源监控
```bash
# 查看CPU和内存使用
top -p $(cat logs/system.pid)

# 查看详细资源使用
htop -p $(cat logs/system.pid)
```

### 日志管理
```bash
# 查看日志大小
du -sh logs/

# 清理旧日志（保留最近7天）
find logs/ -name "system_*.log" -mtime +7 -delete

# 压缩旧日志
find logs/ -name "system_*.log" -mtime +1 -exec gzip {} \;
```

## 🔍 故障排除

### 常见问题

#### 1. 启动失败
```bash
# 检查Python环境
which python
python --version

# 检查依赖
pip list | grep -E "(schedule|asyncio)"

# 检查工作目录
ls -la /data/ai/AJagent-main/
```

#### 2. 进程意外停止
```bash
# 查看系统日志
sudo journalctl -xe

# 查看应用日志
tail -100 logs/system_*.log

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

#### 3. 数据库连接问题
```bash
# 测试数据库连接
python -c "from data_fetcher import DataFetcher; df = DataFetcher(); print('数据库连接正常')"

# 检查网络连接
ping your-database-host
```

## ⚙️ 配置优化

### 环境变量设置
```bash
# 在.bashrc或.profile中添加
export PYTHONPATH="/data/ai/AJagent-main:$PYTHONPATH"
export CASE_PROCESSOR_HOME="/data/ai/AJagent-main"
```

### 日志轮转配置
创建 `/etc/logrotate.d/case-processor`:
```
/data/ai/AJagent-main/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
```

## 🎯 推荐方案

### 开发/测试环境
```bash
# 使用自动化脚本，方便调试
python auto_start_hourly.py
```

### 生产环境
```bash
# 使用systemd服务，稳定可靠
sudo systemctl start case-processor
sudo systemctl enable case-processor
```

### 临时运行
```bash
# 使用nohup，简单快捷
nohup python main_controller.py 1 > logs/temp.log 2>&1 &
```

现在你可以选择最适合的方式来后台运行案件处理系统！





(StAutoGen) [root@localhost AJagent-main]# python auto_start_hourly.py
🚀 自动启动每小时定时任务
==================================================
工作目录: /bigai/ai/AJagent-main
日志文件: logs/hourly_task_20250805_210221.log
PID文件: logs/hourly_task.pid
启动每小时定时任务...
✅ 每小时定时任务已启动
进程ID: 65408
日志文件: logs/hourly_task_20250805_210221.log

📋 管理命令:
查看日志: tail -f logs/hourly_task_20250805_210221.log
查看进程: ps aux | grep 65408
停止任务: kill 65408

🎉 每小时定时任务已成功启动！
系统将立即执行一次任务，然后每小时执行一次。
使用 Ctrl+C 或 kill 65408 停止任务。
(StAutoGen) [root@localhost AJagent-main]#


=====================================================================
pdf:


(StAutoGen) [root@localhost pdf]# nohup ./start_pdf_server.sh  >  start_pdf_server.log 2>&1 &
[1] 85540




