#!/usr/bin/env python3
"""
测试优化版OCR处理功能
"""

import asyncio
import logging
import sys
from datetime import datetime
from ocr_processor import OCRProcessor
from main_controller import MainController


def setup_logging():
    """设置日志配置"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


async def test_ocr_processor():
    """测试OCR处理器的分片功能"""
    print("=" * 60)
    print("测试OCR处理器分片功能")
    print("=" * 60)
    
    processor = OCRProcessor()
    
    # 测试批次
    batch_id = "test_batch_" + datetime.now().strftime('%Y%m%d%H%M%S')
    
    print(f"测试批次: {batch_id}")
    print(f"分片大小: {processor.ocr_batch_size}")
    print(f"超时时间: {processor.ocr_timeout_per_batch}秒")
    
    # 测试获取案件列表
    print("\n1. 测试获取案件列表...")
    cases = processor.get_cases_for_ocr(batch_id)
    print(f"获取到 {len(cases)} 个案件")
    
    if cases:
        # 测试创建分片
        print("\n2. 测试创建分片...")
        chunks = processor.create_batch_chunks(cases)
        print(f"创建了 {len(chunks)} 个分片")
        
        for ppid, chunk_cases in chunks:
            print(f"  分片 {ppid}: {len(chunk_cases)} 个案件")
            for case in chunk_cases[:3]:  # 只显示前3个
                print(f"    - {case['ajbh']}: {case['ajmc']}")
            if len(chunk_cases) > 3:
                print(f"    ... 还有 {len(chunk_cases) - 3} 个案件")
    
    print("\n测试完成!")


async def test_main_controller():
    """测试主控制器的优化处理功能"""
    print("=" * 60)
    print("测试主控制器优化处理功能")
    print("=" * 60)
    
    controller = MainController()
    
    # 测试批次
    batch_id = "test_batch_" + datetime.now().strftime('%Y%m%d%H%M%S')
    
    print(f"测试批次: {batch_id}")
    
    # 测试优化处理方法
    print("\n1. 测试优化版OCR和案件要素提取...")
    result = await controller.process_ocr_and_extraction_optimized(batch_id)
    
    print(f"处理结果: {result['status']}")
    if result['status'] == 'success':
        print(f"OCR成功: {result.get('ocr_successful_count', 0)}")
        print(f"OCR失败: {result.get('ocr_failed_count', 0)}")
        print(f"案件要素提取成功: {result.get('extraction_successful_count', 0)}")
        print(f"案件要素提取失败: {result.get('extraction_failed_count', 0)}")
    else:
        print(f"处理失败: {result.get('error')}")
    
    print("\n测试完成!")


async def test_full_workflow():
    """测试完整的优化工作流程"""
    print("=" * 60)
    print("测试完整的优化工作流程")
    print("=" * 60)
    
    controller = MainController()
    
    # 使用时间范围测试
    stime = "2025-01-01 00:00:00"
    etime = "2025-01-02 00:00:00"
    
    print(f"测试时间范围: {stime} - {etime}")
    
    # 执行优化版时间范围任务
    print("\n开始执行优化版时间范围任务...")
    result = await controller.run_time_range_task(stime, etime)
    
    print(f"\n任务执行结果: {result['status']}")
    if result['status'] == 'success':
        print(f"批次号: {result.get('batch_id')}")
        print(f"总案件数: {result.get('total_cases', 0)}")
        print(f"OCR成功: {result.get('ocr_successful', 0)}")
        print(f"OCR失败: {result.get('ocr_failed', 0)}")
        print(f"案件要素提取成功: {result.get('extraction_successful', 0)}")
        print(f"案件要素提取失败: {result.get('extraction_failed', 0)}")
    else:
        print(f"任务失败: {result.get('error')}")
    
    print("\n测试完成!")


def print_usage():
    """打印使用说明"""
    print("使用方法:")
    print("  python test_optimized_ocr.py 1  # 测试OCR处理器分片功能")
    print("  python test_optimized_ocr.py 2  # 测试主控制器优化处理功能")
    print("  python test_optimized_ocr.py 3  # 测试完整的优化工作流程")


async def main():
    """主函数"""
    setup_logging()
    
    if len(sys.argv) != 2:
        print_usage()
        sys.exit(1)
    
    test_type = int(sys.argv[1])
    
    if test_type == 1:
        await test_ocr_processor()
    elif test_type == 2:
        await test_main_controller()
    elif test_type == 3:
        await test_full_workflow()
    else:
        print("无效的测试类型")
        print_usage()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
