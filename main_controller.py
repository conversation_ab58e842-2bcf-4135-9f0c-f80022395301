#!/usr/bin/env python3
"""
主控制脚本
支持多种任务模式：临时跑、定时任务等
整合所有智能体，实现完整的案件处理流程
"""

import sys
import asyncio
import logging
import schedule
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# 导入各个模块
from data_fetcher import DataFetcher
from pdf_downloader import PDFDownloader
from pdf_merger import PDFMerger
from ocr_processor import OCRProcessor
from case_extraction_agent import CaseExtractionAgent


class MainController:
    """主控制器"""
    
    def __init__(self):
        # 设置日志
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个智能体
        self.data_fetcher = DataFetcher()
        self.pdf_downloader = PDFDownloader()
        self.pdf_merger = PDFMerger()
        self.ocr_processor = OCRProcessor()
        self.case_extraction_agent = CaseExtractionAgent()
        
    def setup_logging(self):
        """设置日志配置"""
        import os
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        log_file = f"{log_dir}/main_controller_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    async def process_batch(self, batch_id: str, cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        处理一个批次的案件
        
        Args:
            batch_id: 批次号
            cases: 案件列表
            
        Returns:
            处理结果
        """
        try:
            self.logger.info(f"开始处理批次: {batch_id}, 案件数量: {len(cases)}")
            
            # 1. 下载PDF文书
            self.logger.info(f"开始下载PDF文书，批次: {batch_id}")
            download_result = await self.pdf_downloader.batch_download_pdfs(batch_id, cases)
            
            if download_result["status"] != "success":
                raise Exception(f"PDF下载失败: {download_result.get('error')}")
            
            # 更新下载状态
            successful_ajbhs = [item["ajbh"] for item in download_result["successful_downloads"]]
            failed_ajbhs = [item["ajbh"] for item in download_result["failed_downloads"]]
            self.pdf_downloader.update_download_status(batch_id, successful_ajbhs, failed_ajbhs)

            # 2. PDF合并
            self.logger.info(f"开始PDF合并，批次: {batch_id}")
            merge_result = self.pdf_merger.batch_merge_pdfs(
                batch_id=batch_id,
                delete_originals=True  # 合并后删除原文件
            )

            if merge_result["status"] != "success":
                self.logger.error(f"PDF合并失败: {merge_result.get('error')}")
                # PDF合并失败不中断流程，继续处理
            else:
                self.logger.info(f"PDF合并完成 - 成功: {merge_result['successful_count']}, 失败: {merge_result['failed_count']}")

            # 3. 优化版OCR识别和案件要素提取
            self.logger.info(f"开始优化版OCR识别和案件要素提取，批次: {batch_id}")
            processing_result = await self.process_ocr_and_extraction_optimized(batch_id)

            if processing_result["status"] != "success":
                self.logger.error(f"优化处理失败: {processing_result.get('error')}")
            else:
                self.logger.info(f"优化处理完成 - OCR成功: {processing_result.get('ocr_successful_count', 0)}, 案件要素提取成功: {processing_result.get('extraction_successful_count', 0)}")

            self.logger.info(f"批次处理完成: {batch_id}")

            return {
                "status": "success",
                "batch_id": batch_id,
                "total_cases": len(cases),
                "download_result": download_result,
                "merge_result": merge_result,
                "processing_result": processing_result,
                "ocr_successful": processing_result.get('ocr_successful_count', 0),
                "ocr_failed": processing_result.get('ocr_failed_count', 0),
                "extraction_successful": processing_result.get('extraction_successful_count', 0),
                "extraction_failed": processing_result.get('extraction_failed_count', 0),
                "summary": f"批次 {batch_id} 优化处理完成"
            }
            
        except Exception as e:
            self.logger.error(f"批次处理失败: {batch_id} - {e}")
            
            return {
                "status": "error",
                "batch_id": batch_id,
                "error": str(e)
            }
    

    async def run_time_range_task(self, stime: str, etime: str, ajbh: str = None) -> Dict[str, Any]:
        """
        运行时间范围任务
        
        Args:
            stime: 开始时间
            etime: 结束时间
            ajbh: 案件编号（可选）
            
        Returns:
            处理结果
        """
        try:
            self.logger.info(f"开始时间范围任务，时间: {stime} - {etime}, 案件: {ajbh or '全部'}")
            
            # 1. 获取案件数据
            fetch_result = self.data_fetcher.fetch_cases_by_time_range(stime, etime, ajbh)
            
            if fetch_result["status"] != "success":
                raise Exception(f"数据获取失败: {fetch_result.get('error')}")
            
            if fetch_result["total_count"] == 0:
                self.logger.info(f"没有找到需要处理的案件")
                return {
                    "status": "success",
                    "message": "没有需要处理的案件",
                    "time_range": f"{stime} - {etime}",
                    "ajbh_filter": ajbh
                }
            
            # 2. 生成批次号并插入案件关系记录
            batch_id = self.data_fetcher.generate_batch_id()
            insert_result = self.data_fetcher.insert_case_relations(
                fetch_result["cases"], batch_id, stime, etime, ajbh
            )
            
            if insert_result["status"] != "success":
                raise Exception(f"插入案件关系记录失败: {insert_result.get('error')}")
            
            # 3. 处理批次
            batch_result = await self.process_batch(batch_id, fetch_result["cases"])
            
            return {
                "status": "success",
                "task_type": "time_range",
                "time_range": f"{stime} - {etime}",
                "ajbh_filter": ajbh,
                "batch_id": batch_id,
                "batch_result": batch_result
            }
            
        except Exception as e:
            self.logger.error(f"时间范围任务失败: {e}")
            return {
                "status": "error",
                "task_type": "time_range",
                "error": str(e)
            }

    async def process_ocr_and_extraction_optimized(self, batch_id: str) -> Dict[str, Any]:
        """
        优化版OCR识别和案件要素提取
        每个分片完成OCR后立即进行案件要素提取

        Args:
            batch_id: 批次号

        Returns:
            处理结果
        """
        try:
            self.logger.info(f"开始优化版OCR和案件要素提取，批次: {batch_id}")

            # 1. 获取需要OCR处理的案件
            cases = self.ocr_processor.get_cases_for_ocr(batch_id)

            if not cases:
                self.logger.info(f"没有需要OCR处理的案件，批次: {batch_id}")
                return {
                    "status": "success",
                    "batch_id": batch_id,
                    "message": "没有需要OCR处理的案件"
                }

            # 2. 创建分片
            chunks = self.ocr_processor.create_batch_chunks(cases)

            if not chunks:
                return {
                    "status": "error",
                    "batch_id": batch_id,
                    "error": "无法创建分片"
                }

            self.logger.info(f"创建了 {len(chunks)} 个分片，每片最多 {self.ocr_processor.ocr_batch_size} 个案件")

            # 3. 按顺序处理每个分片
            total_ocr_successful = 0
            total_ocr_failed = 0
            total_extraction_successful = 0
            total_extraction_failed = 0
            chunk_results = []

            for ppid, chunk_cases in chunks:
                try:
                    self.logger.info(f"开始处理分片 {ppid}，包含 {len(chunk_cases)} 个案件")

                    # 准备分片输入目录
                    if not self.ocr_processor.prepare_chunk_input_directory(batch_id, ppid, chunk_cases):
                        self.logger.error(f"分片 {ppid} 输入目录准备失败")
                        # 标记这个分片的案件为失败
                        self.ocr_processor.update_chunk_failed_cases(batch_id, chunk_cases, f"分片 {ppid} 输入目录准备失败")
                        total_ocr_failed += len(chunk_cases)
                        continue

                    # 处理分片OCR
                    chunk_ocr_result = await self.ocr_processor.process_single_chunk(batch_id, ppid, chunk_cases)

                    if chunk_ocr_result["status"] == "success":
                        ocr_successful = chunk_ocr_result.get("successful_count", 0)
                        ocr_failed = chunk_ocr_result.get("failed_count", 0)
                        total_ocr_successful += ocr_successful
                        total_ocr_failed += ocr_failed

                        self.logger.info(f"分片 {ppid} OCR完成，成功: {ocr_successful}, 失败: {ocr_failed}")

                        # 立即进行这个分片的案件要素提取
                        if ocr_successful > 0:
                            self.logger.info(f"开始分片 {ppid} 的案件要素提取")
                            extraction_result = await self.process_chunk_extraction(batch_id, ppid)

                            if extraction_result["status"] == "success":
                                total_extraction_successful += extraction_result.get("successful_count", 0)
                                total_extraction_failed += extraction_result.get("failed_count", 0)
                            else:
                                total_extraction_failed += ocr_successful

                            chunk_ocr_result["extraction_result"] = extraction_result
                    else:
                        total_ocr_failed += len(chunk_cases)

                    chunk_results.append(chunk_ocr_result)
                    self.logger.info(f"分片 {ppid} 完整处理完成")

                except Exception as e:
                    self.logger.error(f"分片 {ppid} 处理异常: {e}")
                    # 标记这个分片的案件为失败
                    self.ocr_processor.update_chunk_failed_cases(batch_id, chunk_cases, f"分片 {ppid} 处理异常: {str(e)}")
                    total_ocr_failed += len(chunk_cases)

            self.logger.info(f"优化版OCR和案件要素提取完成，批次: {batch_id}")
            self.logger.info(f"OCR - 成功: {total_ocr_successful}, 失败: {total_ocr_failed}")
            self.logger.info(f"案件要素提取 - 成功: {total_extraction_successful}, 失败: {total_extraction_failed}")

            return {
                "status": "success",
                "batch_id": batch_id,
                "total_chunks": len(chunks),
                "ocr_successful_count": total_ocr_successful,
                "ocr_failed_count": total_ocr_failed,
                "extraction_successful_count": total_extraction_successful,
                "extraction_failed_count": total_extraction_failed,
                "chunk_results": chunk_results,
                "summary": f"优化处理完成，OCR成功 {total_ocr_successful} 个，案件要素提取成功 {total_extraction_successful} 个"
            }

        except Exception as e:
            error_msg = f"优化版OCR和案件要素提取异常: {str(e)}"
            self.logger.error(error_msg)

            return {
                "status": "error",
                "batch_id": batch_id,
                "error": error_msg
            }

    async def process_chunk_extraction(self, batch_id: str, ppid: str) -> Dict[str, Any]:
        """
        处理分片的案件要素提取

        Args:
            batch_id: 批次号
            ppid: 分片ID

        Returns:
            处理结果
        """
        try:
            # 获取这个分片中需要提取的案件
            extraction_cases = self.case_extraction_agent.get_cases_for_extraction(batch_id)

            if not extraction_cases:
                return {
                    "status": "success",
                    "batch_id": batch_id,
                    "ppid": ppid,
                    "successful_count": 0,
                    "failed_count": 0,
                    "message": "没有需要提取的案件"
                }

            # 并发处理案件要素提取
            extraction_tasks = []
            for case_info in extraction_cases:
                task = self.case_extraction_agent.process_single_case_complete(case_info)
                extraction_tasks.append(task)

            # 控制并发数
            semaphore = asyncio.Semaphore(10)  # 最大并发数10

            async def process_with_semaphore(task):
                async with semaphore:
                    return await task

            # 执行并发任务
            extraction_results = await asyncio.gather(
                *[process_with_semaphore(task) for task in extraction_tasks],
                return_exceptions=True
            )

            # 统计结果
            successful_count = 0
            failed_count = 0

            for result in extraction_results:
                if isinstance(result, Exception):
                    failed_count += 1
                    self.logger.error(f"案件要素提取异常: {result}")
                elif result and result.get("status") == "success":
                    successful_count += 1
                else:
                    failed_count += 1

            self.logger.info(f"分片 {ppid} 案件要素提取完成 - 成功: {successful_count}, 失败: {failed_count}")

            return {
                "status": "success",
                "batch_id": batch_id,
                "ppid": ppid,
                "successful_count": successful_count,
                "failed_count": failed_count,
                "extraction_results": extraction_results,
                "message": f"分片 {ppid} 案件要素提取完成"
            }

        except Exception as e:
            error_msg = f"分片 {ppid} 案件要素提取异常: {str(e)}"
            self.logger.error(error_msg)

            return {
                "status": "error",
                "batch_id": batch_id,
                "ppid": ppid,
                "error": error_msg,
                "successful_count": 0,
                "failed_count": len(extraction_cases) if 'extraction_cases' in locals() else 0
            }

    async def run_ajbh_task(self, ajbh: str) -> Dict[str, Any]:
        """
        运行案件编号任务
        
        Args:
            ajbh: 案件编号
            
        Returns:
            处理结果
        """
        try:
            self.logger.info(f"开始案件编号任务，案件: {ajbh}")
            
            # 1. 获取案件数据
            fetch_result = self.data_fetcher.fetch_cases_by_ajbh(ajbh)
            
            if fetch_result["status"] != "success":
                raise Exception(f"数据获取失败: {fetch_result.get('error')}")
            
            if fetch_result["total_count"] == 0:
                self.logger.info(f"没有找到案件: {ajbh}")
                return {
                    "status": "success",
                    "message": f"没有找到案件: {ajbh}",
                    "ajbh": ajbh
                }
            
            # 2. 生成批次号并插入案件关系记录
            batch_id = self.data_fetcher.generate_batch_id()
            insert_result = self.data_fetcher.insert_case_relations(
                fetch_result["cases"], batch_id, ajbh=ajbh
            )
            
            if insert_result["status"] != "success":
                raise Exception(f"插入案件关系记录失败: {insert_result.get('error')}")
            
            # 3. 处理批次
            batch_result = await self.process_batch(batch_id, fetch_result["cases"])
            
            return {
                "status": "success",
                "task_type": "ajbh",
                "ajbh": ajbh,
                "batch_id": batch_id,
                "batch_result": batch_result
            }
            
        except Exception as e:
            self.logger.error(f"案件编号任务失败: {e}")
            return {
                "status": "error",
                "task_type": "ajbh",
                "error": str(e)
            }


def main():
    """主函数"""
    controller = MainController()
    
    # 解析命令行参数
    if len(sys.argv) < 2:
        print("用法:")
        print("  临时跑（时间范围）: python main_controller.py 0 \"2025-01-03 09:00:00\" \"2025-01-04 09:00:00\"")
        print("  临时跑（时间范围+案件）: python main_controller.py 0 \"2025-01-03 09:00:00\" \"2025-01-04 09:00:00\" \"A4401171601002021036001\"")
        print("  临时跑（案件编号）: python main_controller.py 0 \"A4401171601002021036001\"")
        print("  定时任务（每小时）: python main_controller.py 1")
        print("  定时任务（每12小时）: python main_controller.py 2")
        print("  定时任务（每24小时）: python main_controller.py 3")
        sys.exit(1)
    
    task_type = int(sys.argv[1])
    
    if task_type == 0:  # 临时跑
        if len(sys.argv) == 3:
            # 单个案件编号
            ajbh = sys.argv[2]
            result = asyncio.run(controller.run_ajbh_task(ajbh))
            
        elif len(sys.argv) == 4:
            # 时间范围
            stime = sys.argv[2]
            etime = sys.argv[3]
            result = asyncio.run(controller.run_time_range_task(stime, etime))
            
        elif len(sys.argv) == 5:
            # 时间范围 + 案件编号
            stime = sys.argv[2]
            etime = sys.argv[3]
            ajbh = sys.argv[4]
            result = asyncio.run(controller.run_time_range_task(stime, etime, ajbh))
            
        else:
            print("临时跑参数错误")
            sys.exit(1)
        
        print(f"任务执行结果: {result}")
        sys.exit(0 if result["status"] == "success" else 1)
    
    elif task_type in [1, 2, 3, 4]:  # 定时任务
        def job():
            """定时任务执行函数"""
            try:
                now = datetime.now()
                
                if task_type == 1:  # 每小时
                    # 处理上一个小时的数据
                    start_time = (now - timedelta(hours=1)).replace(minute=0, second=0, microsecond=0)
                    end_time = start_time + timedelta(hours=1) - timedelta(seconds=1)
                    
                elif task_type == 2:  # 每12小时
                    if now.hour == 13:  # 13点处理0-12点
                        start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
                        end_time = now.replace(hour=12, minute=59, second=59, microsecond=999999)
                    elif now.hour == 0:  # 0点处理前一天13-24点
                        yesterday = now - timedelta(days=1)
                        start_time = yesterday.replace(hour=13, minute=0, second=0, microsecond=0)
                        end_time = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
                    else:
                        return  # 不在执行时间
                        
                elif task_type == 3:  # 每24小时
                    # 0点处理前一天的数据
                    if now.hour != 0:
                        return  # 不在执行时间
                    yesterday = now - timedelta(days=1)
                    start_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
                    end_time = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)

                elif task_type == 4:  # 每24小时
                    # 处理当天的数据
                    start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
                    end_time = now.replace(hour=23, minute=59, second=59, microsecond=999999)


                stime = start_time.strftime("%Y-%m-%d %H:%M:%S")
                etime = end_time.strftime("%Y-%m-%d %H:%M:%S")
                
                controller.logger.info(f"定时任务开始执行，类型: {task_type}, 时间范围: {stime} - {etime}")
                
                result = asyncio.run(controller.run_time_range_task(stime, etime))
                
                if result["status"] == "success":
                    controller.logger.info(f"定时任务执行成功: {result}")
                else:
                    controller.logger.error(f"定时任务执行失败: {result}")
                    
            except Exception as e:
                controller.logger.error(f"定时任务异常: {e}")
        
        # 设置定时任务
        if task_type == 1:  # 每小时
            schedule.every().hour.at(':30').do(job)
            print("每小时定时任务已启动...")
        elif task_type == 2:  # 每12小时
            schedule.every().day.at("13:00").do(job)
            schedule.every().day.at("00:00").do(job)
            print("每12小时定时任务已启动...")
        elif task_type == 3:  # 每24小时
            schedule.every().day.at("00:00").do(job)
            print("每24小时定时任务已启动...")
        elif task_type == 4:  # 每24小时（处理当天数据）
            #schedule.every().day.at("23:30").do(job)  # 每天23:30执行，处理当天数据
            schedule.every().day.at("05:30").do(job)  # 每天05:30执行，处理当天数据            
            print("每24小时定时任务已启动（处理当天数据）...")
        
        # 立即执行一次任务
        print("启动时立即执行一次任务...")
        job()

        # 运行定时任务
        while True:
            print("检查是否有定时任务需要执行...")
            schedule.run_pending()
            print("休眠1小时...")
            time.sleep(60 * 60)  # 休眠1小时
    
    else:
        print(f"未知的任务类型: {task_type}")
        sys.exit(1)


if __name__ == "__main__":
    main()
