#!/usr/bin/env python3
"""
测试数据库表结构迁移
验证ds_case_relation表字段修改是否正确
"""

import sys
import os
import pymysql
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append('/data/ai/AJagent-main')

def get_db_connection():
    """获取数据库连接"""
    try:
        from html_main.web_case_viewer import get_db_connection
        return get_db_connection()
    except ImportError:
        # 如果无法导入，使用默认配置
        return pymysql.connect(
            host='localhost',
            user='root',
            password='your_password',
            database='djzs_db',
            charset='utf8mb4'
        )

def test_table_structure():
    """测试表结构"""
    print("🔍 测试表结构")
    print("="*50)
    
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                
                # 获取表结构
                cursor.execute("DESCRIBE ds_case_relation")
                columns = cursor.fetchall()
                
                print("📋 当前表结构:")
                for col in columns:
                    print(f"   {col['Field']}: {col['Type']} - {col['Comment']}")
                
                # 检查必需的字段
                required_fields = {
                    'llsj': '录入时间',
                    'tbgxsj': '同步更新时间', 
                    'codesource': '图代码来源'
                }
                
                removed_fields = ['tfsj', 'xgsj', 'updatetype']
                
                existing_fields = [col['Field'] for col in columns]
                
                print(f"\n✅ 检查新增字段:")
                for field, desc in required_fields.items():
                    if field in existing_fields:
                        print(f"   ✅ {field} ({desc}): 存在")
                    else:
                        print(f"   ❌ {field} ({desc}): 缺失")
                
                print(f"\n✅ 检查删除字段:")
                for field in removed_fields:
                    if field not in existing_fields:
                        print(f"   ✅ {field}: 已删除")
                    else:
                        print(f"   ❌ {field}: 仍存在")
                
                return True
                
    except Exception as e:
        print(f"❌ 测试表结构失败: {e}")
        return False

def test_data_integrity():
    """测试数据完整性"""
    print(f"\n🔍 测试数据完整性")
    print("="*50)
    
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                
                # 检查记录总数
                cursor.execute("SELECT COUNT(*) as total FROM ds_case_relation")
                total = cursor.fetchone()['total']
                print(f"📊 总记录数: {total}")
                
                # 检查有效记录数
                cursor.execute("SELECT COUNT(*) as active FROM ds_case_relation WHERE isdelete = '0'")
                active = cursor.fetchone()['active']
                print(f"📊 有效记录数: {active}")
                
                # 检查新字段的数据
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total,
                        COUNT(llsj) as has_llsj,
                        COUNT(tbgxsj) as has_tbgxsj,
                        COUNT(codesource) as has_codesource
                    FROM ds_case_relation 
                    WHERE isdelete = '0'
                """)
                
                field_stats = cursor.fetchone()
                print(f"\n📊 新字段数据统计:")
                print(f"   llsj (录入时间): {field_stats['has_llsj']}/{field_stats['total']}")
                print(f"   tbgxsj (同步更新时间): {field_stats['has_tbgxsj']}/{field_stats['total']}")
                print(f"   codesource (图代码来源): {field_stats['has_codesource']}/{field_stats['total']}")
                
                # 检查codesource字段的值分布
                cursor.execute("""
                    SELECT codesource, COUNT(*) as count
                    FROM ds_case_relation 
                    WHERE isdelete = '0'
                    GROUP BY codesource
                """)
                
                codesource_stats = cursor.fetchall()
                print(f"\n📊 图代码来源分布:")
                for stat in codesource_stats:
                    source_text = 'AI生成' if stat['codesource'] == '0' else '人工修改' if stat['codesource'] == '1' else '未知'
                    print(f"   {stat['codesource']} ({source_text}): {stat['count']} 条")
                
                return True
                
    except Exception as e:
        print(f"❌ 测试数据完整性失败: {e}")
        return False

def test_application_compatibility():
    """测试应用程序兼容性"""
    print(f"\n🔍 测试应用程序兼容性")
    print("="*50)
    
    try:
        # 测试data_fetcher.py
        print("📦 测试 data_fetcher.py...")
        try:
            from data_fetcher import DataFetcher
            df = DataFetcher()
            print("   ✅ data_fetcher.py 导入成功")
        except Exception as e:
            print(f"   ❌ data_fetcher.py 导入失败: {e}")
        
        # 测试web_case_viewer.py
        print("📦 测试 web_case_viewer.py...")
        try:
            sys.path.append('/data/ai/AJagent-main/html_main')
            from web_case_viewer import get_codesource_text
            result = get_codesource_text('0')
            if result == 'AI生成':
                print("   ✅ web_case_viewer.py 函数正常")
            else:
                print(f"   ❌ web_case_viewer.py 函数异常: {result}")
        except Exception as e:
            print(f"   ❌ web_case_viewer.py 测试失败: {e}")
        
        # 测试SQL查询
        print("📦 测试 SQL查询...")
        try:
            with get_db_connection() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    
                    # 测试新的查询语句
                    cursor.execute("""
                        SELECT batchid, ajbh, ajmc, llsj, counts, xxzjbh, ajlx, 
                               flwsxzdz, tbrksj, tbgxsj, codesource, status
                        FROM ds_case_relation
                        WHERE isdelete = '0'
                        LIMIT 1
                    """)
                    
                    result = cursor.fetchone()
                    if result:
                        print("   ✅ 新SQL查询成功")
                        print(f"   示例记录: {result['ajbh']}")
                    else:
                        print("   ⚠️  查询结果为空")
                        
        except Exception as e:
            print(f"   ❌ SQL查询测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试应用程序兼容性失败: {e}")
        return False

def test_migration_script():
    """测试迁移脚本"""
    print(f"\n🔍 测试迁移脚本")
    print("="*50)
    
    migration_file = "migrate_table_structure.sql"
    
    if os.path.exists(migration_file):
        print(f"✅ 迁移脚本存在: {migration_file}")
        
        # 读取脚本内容
        with open(migration_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键操作
        operations = [
            "DROP COLUMN `tfsj`",
            "DROP COLUMN `xgsj`", 
            "ADD COLUMN `llsj`",
            "ADD COLUMN `tbgxsj`",
            "CHANGE COLUMN `updatetype` `codesource`"
        ]
        
        print("📋 迁移操作检查:")
        for op in operations:
            if op in content:
                print(f"   ✅ {op}")
            else:
                print(f"   ❌ {op}")
        
        return True
    else:
        print(f"❌ 迁移脚本不存在: {migration_file}")
        return False

def main():
    """主函数"""
    
    print("🧪 数据库表结构迁移测试")
    print("="*60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试1: 表结构
    test1_result = test_table_structure()
    
    # 测试2: 数据完整性
    test2_result = test_data_integrity()
    
    # 测试3: 应用程序兼容性
    test3_result = test_application_compatibility()
    
    # 测试4: 迁移脚本
    test4_result = test_migration_script()
    
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print("="*60)
    
    print(f"表结构检查: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"数据完整性检查: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"应用程序兼容性: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"迁移脚本检查: {'✅ 通过' if test4_result else '❌ 失败'}")
    
    if all([test1_result, test2_result, test3_result, test4_result]):
        print(f"\n🎉 表结构迁移测试通过！")
        print(f"✅ 字段修改正确: tfsj,xgsj → llsj,tbgxsj")
        print(f"✅ 字段重命名正确: updatetype → codesource")
        print(f"✅ 应用程序兼容性良好")
        print(f"✅ 数据完整性保持")
    else:
        print(f"\n⚠️  部分测试失败，需要进一步检查")
    
    return all([test1_result, test2_result, test3_result, test4_result])

if __name__ == "__main__":
    main()
