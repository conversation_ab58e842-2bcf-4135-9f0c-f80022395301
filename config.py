#!/usr/bin/env python3
"""
配置文件
存放数据库连接信息和大模型连接信息
"""

from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo


class Config:
    """配置类"""
    
    def __init__(self):
        # MySQL数据库连接信息
        self.db_config = {
            #'host': '***********', 
            'host': '*************', 
            'user': 'root',
            'password': '123456',
            'database': 'djzs_db', 
            'charset': 'utf8mb4'
        }
        
        '''
        self.db_config = { 
            'host': '************',
            'user': 'root',
            'password': 'root@Nexwise',
            'database': 'ds_prov_base_pro', 
            'charset': 'utf8mb4'
        }
        '''
        
        # 大模型连接信息
        self.model_client = OpenAIChatCompletionClient(
            model="Qwen3-32B",
            #base_url="http://***********:8011/v1",
            base_url="http://*************:8011/v1",
            api_key="jc888",
            model_info=ModelInfo(
                family="openai",
                vision=True,
                function_calling=True,
                json_output=True
            )
        )
        
        # 数据库表名配置
        self.table_config = {
            # 源数据表
            #'source_table': 'ds_case_instrument_his_ai',  # 案件文书历史表（AI版专用）
            'source_table': 'ds_case_instrument_his',  # 案件文书历史表
            
            # 主要业务表
            'case_relation_table': 'ds_case_relation',     # 案件关系图表
            'case_details_table': 'ds_case_details',       # 案件详细信息表

            # 备份表
            #'case_relation_backup': 'ds_case_relation_backup',
            #'case_details_backup': 'ds_case_details_backup',

            # 视图名称
            #'case_relation_active_view': 'ds_case_relation_active',
            #'case_details_active_view': 'ds_case_details_active'
        }

        # 系统配置
        self.system_config = {
            'max_concurrent': 10,  # 最大并发数
            'max_repair_attempts': 3,  # 修复智能体最大尝试次数
            #'ocr_base_path': '/bigai/ai/MonkeyOCR',  # OCR基础路径 
            #'ocr_base_path': '/data/ai/MonkeyOCR',  # OCR基础路径
            #'ocr_base_path': '/bigai/ai/AJagent-main/data',  # OCR基础路径，用工程路径 GS            
            'ocr_base_path': '/data/ai/AJagent-main/data',  # OCR数据基础路径，用工程路径  DS  
            'docker_container': 'monkeyocr',  # Docker容器名
            'download_timeout': 60,  # 下载超时时间(秒)

            # Mermaid验证配置
            'mermaid_validation_method': 'docker',  # 验证方法：'docker'：Mermaid Docker镜像, 'live_editor'：编辑器, 'hybrid'：混合，先编辑器，失败再用Mermaid Docker镜像
            'mermaid_docker_image': 'minlag/mermaid-cli:latest',  # Mermaid Docker镜像
            #'mermaid_live_editor_url': 'http://***********:8008',  # 本地Mermaid Live Editor URL
            'mermaid_live_editor_url': 'http://*************:8000/',  # 本地Mermaid Live Editor URL
            'enable_quick_syntax_check': True,  # 是否启用快速语法检查
            'docker_validation_timeout': 30,  # 验证超时时间(秒)
        }
    
    def get_db_config(self):
        """获取数据库配置"""
        return self.db_config

    def get_model_client(self):
        """获取模型客户端"""
        return self.model_client

    def get_system_config(self):
        """获取系统配置"""
        return self.system_config

    def get_table_config(self):
        """获取表名配置"""
        return self.table_config

    def get_table_name(self, table_key):
        """获取指定的表名"""
        return self.table_config.get(table_key, table_key)


# 全局配置实例
config = Config()
