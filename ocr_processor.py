#!/usr/bin/env python3
"""
OCR识别智能体
对下载的法律文书PDF进行OCR识别，将识别结果保存为案件内容
"""

import os
import subprocess
import logging
import pymysql
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path
from config import config


class OCRProcessor:
    """OCR处理器"""
    
    def __init__(self):
        self.db_config = config.get_db_config()
        self.system_config = config.get_system_config()
        self.table_config = config.get_table_config()
        self.logger = logging.getLogger(__name__)

        # 表名配置
        self.case_relation_table = self.table_config['case_relation_table']
        
        # OCR配置
        self.docker_container = self.system_config['docker_container']
        self.ocr_base_path = self.system_config['ocr_base_path']
        
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def setup_ocr_directories(self, batch_id: str) -> Dict[str, str]:
        """
        设置OCR处理目录
        
        Args:
            batch_id: 批次号
            
        Returns:
            目录路径字典
        """
        try:
            input_dir = f"{self.ocr_base_path}/{batch_id}/input"
            output_dir = f"{self.ocr_base_path}/{batch_id}/output"
            
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 设置目录权限
            chmod_cmd = f"chmod -R 777 {self.ocr_base_path}/{batch_id}/output"
            subprocess.run(chmod_cmd, shell=True, check=True)
            
            self.logger.info(f"OCR目录设置完成 - 输入: {input_dir}, 输出: {output_dir}")
            
            return {
                "input_dir": input_dir,
                "output_dir": output_dir,
                "batch_dir": f"{self.ocr_base_path}/{batch_id}"
            }
            
        except Exception as e:
            self.logger.error(f"OCR目录设置失败: {e}")
            raise
    
    def run_ocr_processing(self, batch_id: str) -> Dict[str, Any]:
        """
        运行OCR处理
        
        Args:
            batch_id: 批次号
            
        Returns:
            OCR处理结果
        """
        try:
            # 设置目录
            dirs = self.setup_ocr_directories(batch_id)
            
            # 检查输入目录是否存在PDF文件
            input_dir = dirs["input_dir"]
            if not os.path.exists(input_dir):
                raise Exception(f"输入目录不存在: {input_dir}")
            
            pdf_files = [f for f in os.listdir(input_dir) if f.endswith('.pdf')]
            if not pdf_files:
                raise Exception(f"输入目录中没有PDF文件: {input_dir}")
            
            self.logger.info(f"开始OCR处理，批次: {batch_id}, PDF文件数: {len(pdf_files)}")
            
            # 构建Docker命令
            docker_cmd = [
                "docker", "exec", self.docker_container,
                "python", "parse.py", 
                f"./data/{batch_id}/input",
                "-o", f"./data/{batch_id}/output"
            ]
            
            # 执行OCR处理
            result = subprocess.run(
                docker_cmd,
                capture_output=True,
                text=True,
                timeout=3600  # 1小时超时
            )
            
            if result.returncode == 0:
                self.logger.info(f"OCR处理成功，批次: {batch_id}")
                
                return {
                    "status": "success",
                    "batch_id": batch_id,
                    "pdf_count": len(pdf_files),
                    "output_dir": dirs["output_dir"],
                    "stdout": result.stdout,
                    "message": "OCR处理成功"
                }
            else:
                error_msg = f"OCR处理失败: {result.stderr}"
                self.logger.error(error_msg)
                
                return {
                    "status": "error",
                    "batch_id": batch_id,
                    "error": error_msg,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "returncode": result.returncode
                }
                
        except subprocess.TimeoutExpired:
            error_msg = f"OCR处理超时: {batch_id}"
            self.logger.error(error_msg)
            return {
                "status": "error",
                "batch_id": batch_id,
                "error": error_msg
            }
            
        except Exception as e:
            error_msg = f"OCR处理异常: {str(e)}"
            self.logger.error(error_msg)
            return {
                "status": "error",
                "batch_id": batch_id,
                "error": error_msg
            }
    
    def read_ocr_results(self, batch_id: str) -> Dict[str, Any]:
        """
        读取OCR识别结果
        
        Args:
            batch_id: 批次号
            
        Returns:
            OCR结果字典，key为案件编号，value为案件内容
        """
        try:
            output_dir = f"{self.ocr_base_path}/{batch_id}/output"
            
            if not os.path.exists(output_dir):
                raise Exception(f"OCR输出目录不存在: {output_dir}")
            
            ocr_results = {}
            
            # 遍历输出目录中的子目录
            for item in os.listdir(output_dir):
                item_path = os.path.join(output_dir, item)
                
                if os.path.isdir(item_path):
                    # 假设目录名就是案件编号
                    ajbh = item
                    md_file_path = os.path.join(item_path, f"{ajbh}.md")
                    
                    if os.path.exists(md_file_path):
                        try:
                            with open(md_file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                
                            if content.strip():
                                ocr_results[ajbh] = content
                                self.logger.info(f"读取OCR结果成功: {ajbh}, 内容长度: {len(content)}")
                            else:
                                self.logger.warning(f"OCR结果为空: {ajbh}")
                                
                        except Exception as e:
                            self.logger.error(f"读取OCR结果失败: {ajbh} - {e}")
                    else:
                        self.logger.warning(f"OCR结果文件不存在: {md_file_path}")
            
            self.logger.info(f"读取OCR结果完成，批次: {batch_id}, 成功: {len(ocr_results)}")
            
            return {
                "status": "success",
                "batch_id": batch_id,
                "results": ocr_results,
                "total_count": len(ocr_results)
            }
            
        except Exception as e:
            self.logger.error(f"读取OCR结果失败: {e}")
            return {
                "status": "error",
                "batch_id": batch_id,
                "error": str(e),
                "results": {}
            }
    
    def update_case_content(self, batch_id: str, ocr_results: Dict[str, str]) -> Dict[str, Any]:
        """
        将OCR结果更新到数据库
        
        Args:
            batch_id: 批次号
            ocr_results: OCR结果字典
            
        Returns:
            更新结果
        """
        try:
            if not ocr_results:
                return {
                    "status": "error",
                    "message": "没有OCR结果需要更新"
                }
            
            successful_updates = []
            failed_updates = []
            
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    
                    for ajbh, content in ocr_results.items():
                        try:
                            # 更新案件内容和状态
                            update_sql = f"""
                            UPDATE `{self.case_relation_table}`
                            SET ajnr = %s, status = '2', updatetime = NOW()
                            WHERE batchid = %s AND ajbh = %s
                            """
                            
                            affected_rows = cursor.execute(update_sql, (content, batch_id, ajbh))
                            
                            if affected_rows > 0:
                                successful_updates.append(ajbh)
                                self.logger.info(f"更新案件内容成功: {ajbh}")
                            else:
                                failed_updates.append(ajbh)
                                self.logger.warning(f"更新案件内容失败，未找到记录: {ajbh}")
                                
                        except Exception as e:
                            failed_updates.append(ajbh)
                            self.logger.error(f"更新案件内容异常: {ajbh} - {e}")
                    
                    connection.commit()
            
            self.logger.info(f"OCR结果更新完成 - 成功: {len(successful_updates)}, 失败: {len(failed_updates)}")
            
            return {
                "status": "success",
                "batch_id": batch_id,
                "successful_count": len(successful_updates),
                "failed_count": len(failed_updates),
                "successful_updates": successful_updates,
                "failed_updates": failed_updates,
                "message": f"成功更新 {len(successful_updates)}/{len(ocr_results)} 个案件内容"
            }
            
        except Exception as e:
            self.logger.error(f"更新OCR结果失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "batch_id": batch_id
            }
    
    def update_failed_cases(self, batch_id: str, error_message: str) -> Dict[str, Any]:
        """
        更新失败案件的状态
        
        Args:
            batch_id: 批次号
            error_message: 错误信息
            
        Returns:
            更新结果
        """
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    
                    # 更新所有状态为1的记录为失败状态
                    update_sql = f"""
                    UPDATE `{self.case_relation_table}`
                    SET status = '4', error = %s, updatetime = NOW()
                    WHERE batchid = %s AND status = '1'
                    """
                    
                    affected_rows = cursor.execute(update_sql, (error_message, batch_id))
                    connection.commit()
            
            self.logger.info(f"更新失败案件状态完成，批次: {batch_id}, 影响行数: {affected_rows}")
            
            return {
                "status": "success",
                "affected_rows": affected_rows,
                "message": f"更新了 {affected_rows} 个失败案件的状态"
            }
            
        except Exception as e:
            self.logger.error(f"更新失败案件状态异常: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def process_batch(self, batch_id: str) -> Dict[str, Any]:
        """
        处理整个批次的OCR识别
        
        Args:
            batch_id: 批次号
            
        Returns:
            处理结果
        """
        try:
            self.logger.info(f"开始处理OCR批次: {batch_id}")
            
            # 1. 运行OCR处理
            ocr_result = self.run_ocr_processing(batch_id)
            
            if ocr_result["status"] != "success":
                # OCR处理失败，更新所有案件状态为失败
                self.update_failed_cases(batch_id, ocr_result.get("error", "OCR处理失败"))
                return ocr_result
            
            # 2. 读取OCR结果
            read_result = self.read_ocr_results(batch_id)
            
            if read_result["status"] != "success":
                self.update_failed_cases(batch_id, read_result.get("error", "读取OCR结果失败"))
                return read_result
            
            # 3. 更新数据库
            update_result = self.update_case_content(batch_id, read_result["results"])
            
            self.logger.info(f"OCR批次处理完成: {batch_id}")
            
            return {
                "status": "success",
                "batch_id": batch_id,
                "ocr_processing": ocr_result,
                "content_update": update_result,
                "summary": f"OCR处理完成，成功识别 {update_result.get('successful_count', 0)} 个案件"
            }
            
        except Exception as e:
            error_msg = f"OCR批次处理异常: {str(e)}"
            self.logger.error(error_msg)
            
            # 更新失败状态
            self.update_failed_cases(batch_id, error_msg)
            
            return {
                "status": "error",
                "batch_id": batch_id,
                "error": error_msg
            }


def main():
    """测试函数"""
    logging.basicConfig(level=logging.INFO)
    
    processor = OCRProcessor()
    
    # 测试批次
    batch_id = "2025073009461901"
    
    # 测试OCR处理
    result = processor.process_batch(batch_id)
    print(f"OCR处理结果: {result}")


if __name__ == "__main__":
    main()
