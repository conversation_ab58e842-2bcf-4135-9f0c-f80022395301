#!/usr/bin/env python3
"""
验证定时任务修改
"""

import os

def main():
    print("🔍 验证定时任务修改")
    print("="*50)
    
    # 检查main_controller.py文件
    if not os.path.exists("main_controller.py"):
        print("❌ main_controller.py 文件不存在")
        return
    
    with open("main_controller.py", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修改
    checks = [
        ("立即执行逻辑", "启动时立即执行一次任务"),
        ("job()调用", "job()"),
        ("定时循环", "while True:"),
        ("休眠逻辑", "time.sleep(60 * 60)")
    ]
    
    print("检查修改内容:")
    all_passed = True
    
    for name, pattern in checks:
        if pattern in content:
            print(f"   ✅ {name}: 找到")
        else:
            print(f"   ❌ {name}: 未找到")
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 修改验证通过！")
        print(f"现在定时任务将在启动时立即执行一次，然后按正常间隔执行。")
        print(f"\n使用方法:")
        print(f"python start.py")
        print(f"选择选项 4 (每小时定时任务)")
    else:
        print(f"\n❌ 修改验证失败，请检查代码")

if __name__ == "__main__":
    main()
