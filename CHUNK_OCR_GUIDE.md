# OCR分片批处理使用指南

## 概述

为了解决大批量案件（600+）OCR处理超时问题，我们实现了分片批处理功能。原本按整个批次处理的方式改为将一个批次分为多个片批来处理，每个片批完成后立即进行下一步处理。

## 核心特性

### 1. 分片处理
- **分片大小**：默认20个案件为一个分片，可在config.py中配置
- **分片ID**：ppid，从1开始递增（1, 2, 3, 4...）
- **目录结构**：`{ocr_base_path}/{batch_id}/{ppid}/input/` 和 `{ocr_base_path}/{batch_id}/{ppid}/output/`

### 2. 流水线处理
- 每个分片OCR完成后立即更新数据库状态
- OCR成功的案件（status=2）立即进行案件要素提取
- 无需等待所有分片完成

### 3. 状态管理
- **OCR成功**：ajnr字段更新，status=2
- **OCR失败**：status=4，error字段记录错误信息
- **实时更新**：每个分片完成后立即更新对应案件状态

## 配置说明

### config.py新增配置项

```python
# OCR分片处理配置
'ocr_batch_size': 20,  # OCR分片批处理大小，默认20个案件为一个分片
'ocr_timeout_per_batch': 1800,  # 每个分片的超时时间(秒)，默认30分钟
```

## 核心方法

### OCRProcessor类新增方法

#### 1. 分片管理
```python
get_cases_for_ocr(batch_id)  # 获取需要OCR处理的案件列表
create_batch_chunks(cases)   # 将案件列表分片
prepare_chunk_input_directory(batch_id, ppid)  # 准备分片目录
```

#### 2. 分片处理
```python
process_single_chunk(batch_id, ppid, chunk_cases)  # 处理单个分片
process_batch_with_chunks(batch_id)  # 分片批处理主方法
```

#### 3. 数据库操作
```python
update_chunk_database(batch_id, ppid, ocr_results, chunk_cases)  # 更新分片数据库
update_chunk_failed_cases(batch_id, chunk_cases, error_message)  # 更新失败案件
```

### MainController类新增方法

#### 1. 优化处理
```python
process_ocr_and_extraction_optimized(batch_id)  # 优化版OCR和案件要素提取
process_chunk_extraction(batch_id, ppid)  # 分片案件要素提取
```

## 处理流程

### 原有流程
```
数据获取 → PDF下载 → PDF合并 → OCR识别(整批) → 案件要素提取(整批)
```

### 优化后流程
```
数据获取 → PDF下载 → PDF合并 → 分片OCR处理：
├── 创建分片（每片20个案件）
└── 按顺序处理每个分片：
    ├── 准备分片目录
    ├── 执行分片OCR识别
    ├── 读取分片OCR结果
    ├── 更新数据库状态（成功：status=2，失败：status=4）
    └── 立即进行该分片的案件要素提取
```

## 使用方法

### 1. 测试功能
```bash
# 测试OCR分片处理功能
python test_chunk_ocr.py 1

# 测试优化版工作流程
python test_chunk_ocr.py 2

# 测试单个分片处理
python test_chunk_ocr.py 3
```

### 2. 正常使用
```bash
# 时间范围处理（自动使用分片OCR）
python main_controller.py 0 "2025-08-09 00:00:00" "2025-08-09 23:59:59"

# 单个案件处理
python main_controller.py 0 "A4401171601002021036001"
```

## 目录结构

### 分片目录结构
```
/data/{batch_id}/
├── input/                    # 原始PDF文件目录
├── 1/                       # 分片1
│   ├── input/               # 分片1输入目录
│   └── output/              # 分片1输出目录
├── 2/                       # 分片2
│   ├── input/               # 分片2输入目录
│   └── output/              # 分片2输出目录
└── ...
```

### Docker命令示例
```bash
# 分片模式
docker exec monkeyocr python parse.py ./data/{batch_id}/{ppid}/input -o ./data/{batch_id}/{ppid}/output

# 原有模式
docker exec monkeyocr python parse.py ./data/{batch_id}/input -o ./data/{batch_id}/output
```

## 监控和日志

### 日志输出示例
```
2025-08-09 10:00:00 - 开始分片OCR处理，批次: 2025080910000001
2025-08-09 10:00:01 - 创建了 30 个分片，每片最多 20 个案件
2025-08-09 10:00:02 - 开始处理分片 1，包含 20 个案件
2025-08-09 10:05:30 - 分片 1 OCR完成，成功: 18, 失败: 2
2025-08-09 10:05:31 - 开始分片 1 的案件要素提取
2025-08-09 10:08:45 - 分片 1 案件要素提取完成 - 成功: 16, 失败: 2
2025-08-09 10:08:46 - 分片 1 完整处理完成
```

### 数据库状态监控
```sql
-- 查看批次处理状态
SELECT status, COUNT(*) as count 
FROM ds_case_relation 
WHERE batchid = '2025080910000001' 
GROUP BY status;

-- 查看处理进度
SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN status = '2' THEN 1 ELSE 0 END) as ocr_completed,
    SUM(CASE WHEN status = '3' THEN 1 ELSE 0 END) as extraction_completed,
    SUM(CASE WHEN status = '4' THEN 1 ELSE 0 END) as failed
FROM ds_case_relation 
WHERE batchid = '2025080910000001';
```

## 性能优化

### 1. 调整分片大小
根据服务器性能和案件复杂度调整：
```python
# 高性能服务器
'ocr_batch_size': 30,

# 低性能服务器或复杂案件
'ocr_batch_size': 10,
```

### 2. 调整超时时间
```python
# 简单案件
'ocr_timeout_per_batch': 900,  # 15分钟

# 复杂案件
'ocr_timeout_per_batch': 3600,  # 1小时
```

## 故障排除

### 1. 分片目录创建失败
```bash
# 检查目录权限
ls -la /bigai/ai/AJagent-main/data/

# 手动创建目录
mkdir -p /bigai/ai/AJagent-main/data/{batch_id}/{ppid}/input
mkdir -p /bigai/ai/AJagent-main/data/{batch_id}/{ppid}/output
chmod -R 777 /bigai/ai/AJagent-main/data/{batch_id}
```

### 2. Docker容器问题
```bash
# 检查容器状态
docker ps | grep monkeyocr

# 重启容器
docker restart monkeyocr

# 检查容器内目录
docker exec monkeyocr ls -la ./data/
```

### 3. 数据库连接问题
```bash
# 检查数据库连接
mysql -h 10.10.20.42 -u root -p djzs_db

# 检查表结构
DESCRIBE ds_case_relation;
```

## 优势总结

1. **解决超时问题**：600+案件不再超时
2. **实时处理**：每个分片完成后立即处理
3. **容错性强**：单个分片失败不影响其他分片
4. **资源优化**：避免大批量处理导致的资源耗尽
5. **流水线效率**：OCR和案件要素提取并行进行
6. **可配置性**：分片大小和超时时间可灵活调整

## 注意事项

1. **磁盘空间**：分片处理会创建更多目录，确保有足够磁盘空间
2. **Docker容器**：确保MonkeyOCR容器运行正常
3. **数据库连接**：分片处理会增加数据库连接频率
4. **日志管理**：分片处理会产生更多日志，注意日志文件大小
