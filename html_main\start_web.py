#!/usr/bin/env python3
"""
Web案件查看器启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path


def check_dependencies():
    """检查依赖包"""
    print("检查依赖包...")
    
    try:
        import flask
        import pymysql
        print("✅ 依赖包检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("正在安装依赖包...")
        
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "web_requirements.txt"], 
                         check=True)
            print("✅ 依赖包安装完成")
            return True
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败")
            return False


def test_database_connection():
    """测试数据库连接"""
    print("测试数据库连接...")
    
    try:
        import pymysql
        
        # 使用web_case_viewer.py中的配置
        config = {
            'host': '***********',
            #'host': '*************',
            'user': 'root',
            'password': '123456',
            'database': 'djzs_db',
            'charset': 'utf8mb4'
        }
        
        connection = pymysql.connect(**config)
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM ds_case_relation")
            count = cursor.fetchone()[0]
            print(f"✅ 数据库连接成功，找到 {count} 条案件记录")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


def start_web_server():
    """启动Web服务器"""
    print("启动Web服务器...")
    
    try:
        # 导入并运行Flask应用
        from web_case_viewer import app
        
        print("✅ Web服务器启动成功")
        print("📱 访问地址:")
        print("   本地访问: http://localhost:9998")
        print("   网络访问: http://***********:9998")
        #print("   网络访问: http://*************:9998")
        print("\n🔗 功能说明:")
        print("   - 主页: 案件列表，支持搜索和筛选")
        print("   - 详情: 点击案件编号查看完整信息")
        print("   - 关系图: 点击'关系图'按钮跳转到Mermaid Live Editor（使用streamlit_app.py相同方式）")
        print("   - 预览: 点击'预览'按钮在网页内查看关系图")
        print("   - 调试: 点击'调试'按钮查看URL创建过程和测试不同方法")
        print("   - 统计: 访问 /stats 查看处理统计")
        print("\n按 Ctrl+C 停止服务器")
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open('http://localhost:9998')
        
        import threading
        threading.Thread(target=open_browser, daemon=True).start()
        
        # 启动Flask应用
        app.run(host='0.0.0.0', port=9998, debug=False)
        
    except KeyboardInterrupt:
        print("\n👋 Web服务器已停止")
    except Exception as e:
        print(f"❌ Web服务器启动失败: {e}")


def main():
    """主函数"""
    print("="*60)
    print("🌐 案件关系图查看器 - Web版本")
    print("="*60)
    print("📋 显示所有数据库字段(共22个):")
    print("   - batchid: 数据批次号")
    print("   - ajbh: 案件编号")
    print("   - ajmc: 案件名称")
    print("   - llsj: 录入时间")
    print("   - tbgxsj: 同步更新时间")
    print("   - counts: 意见书数量")
    print("   - xxzjbh: 信息主键编号")
    print("   - ajlx: 案件类型")
    print("   - flwsxzdz: 法律文书下载地址")
    print("   - tbrksj: 同步入库时间")
    print("   - ajnr: 案件内容")
    print("   - code: 关系图代码")
    print("   - lastcode: 最终的关系图代码")
    print("   - updater: 修改人员")
    print("   - codesource: 图代码来源(0:AI生成, 1:人工修改)")
    print("   - status: AI处理状态")
    print("   - starttime: AI开始处理时间")
    print("   - endtime: AI完成处理时间")
    print("   - updatetime: 更新时间")
    print("   - nums: AI重跑次数")
    print("   - error: 错误信息")
    print("   - isdelete: 删除状态(0:有效, 1:已删除)")
    print("")
    print("📊 新增案件详细信息页面:")
    print("   - /case_details: 案件详细信息列表")
    print("   - 显示ds_case_details表的所有31个字段")
    print("   - 支持搜索、筛选和分页功能")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        print("请手动安装依赖包: pip install -r web_requirements.txt")
        return
    
    # 测试数据库
    if not test_database_connection():
        print("请检查数据库配置和连接")
        return
    
    # 启动Web服务器
    start_web_server()


if __name__ == "__main__":
    main()
