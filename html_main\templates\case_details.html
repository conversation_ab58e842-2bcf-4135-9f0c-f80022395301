<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件详细信息列表 - 智慧"双反"平台</title>
    <link href="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.1.0/dist/index.css" rel="stylesheet">
    <style>
        :root {
            --el-color-primary: #409eff;
            --el-bg-color: #0c235b;
            --el-bg-color-page: #f2f3f5;
            --el-bg-color-overlay: #00326f;
            --el-text-color-primary: rgba(255, 255, 255, 0.9);
            --el-text-color-regular: rgba(255, 255, 255, 0.8);
            --el-text-color-secondary: #909399;
            --el-border-color: rgba(255, 255, 255, 0.25);
            --el-border-color-light: #1e3f77;
            --el-fill-color-light: rgba(255, 255, 255, 0.08);
            --el-font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }

        body {
            font-family: var(--el-font-family);
            background: linear-gradient(135deg, #0c235b 0%, #1e3f77 100%);
            min-height: 100vh;
            margin: 0;
            color: var(--el-text-color-primary);
        }

        .main-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .platform-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px 0;
        }

        .platform-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .platform-subtitle {
            font-size: 16px;
            color: var(--el-text-color-regular);
            margin-top: 8px;
        }

        .tab-navigation {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 24px;
            border: 1px solid var(--el-border-color);
        }

        .nav-tabs {
            border: none;
            display: flex;
            gap: 8px;
        }

        .nav-tab {
            flex: 1;
            padding: 16px 24px;
            background: transparent;
            border: none;
            border-radius: 8px;
            color: var(--el-text-color-regular);
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .nav-tab:hover {
            background: rgba(64, 158, 255, 0.1);
            color: var(--el-color-primary);
        }

        .nav-tab.active {
            background: var(--el-color-primary);
            color: white;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            color: #333;
        }
        .search-form {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .search-input, .search-select {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            color: #333;
            transition: all 0.3s ease;
        }

        .search-input:focus, .search-select:focus {
            outline: none;
            border-color: var(--el-color-primary);
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .search-btn {
            background: var(--el-color-primary);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: #3a8ee6;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }

        .reset-btn {
            background: rgba(255, 255, 255, 0.1);
            color: var(--el-text-color-regular);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .reset-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .table-container {
            overflow-x: auto;
            margin-bottom: 24px;
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            background: white;
        }

        .data-table th {
            background: linear-gradient(135deg, #0c235b 0%, #1e3f77 100%);
            color: white;
            font-weight: 600;
            padding: 16px 12px;
            text-align: left;
            border-bottom: 2px solid var(--el-color-primary);
            position: sticky;
            top: 0;
            z-index: 10;
            white-space: nowrap;
            font-size: 14px;
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }

        .data-table tr:hover {
            background-color: rgba(64, 158, 255, 0.05);
        }

        .text-truncate {
            max-width: 100px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .stats-info {
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(64, 158, 255, 0.05) 100%);
            border: 1px solid rgba(64, 158, 255, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            color: #333;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 24px;
        }

        .page-btn {
            padding: 8px 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            color: var(--el-text-color-regular);
            border-radius: 6px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .page-btn:hover {
            background: var(--el-color-primary);
            color: white;
            border-color: var(--el-color-primary);
        }

        .page-btn.active {
            background: var(--el-color-primary);
            color: white;
            border-color: var(--el-color-primary);
        }

        .action-btn {
            padding: 6px 12px;
            border: 1px solid var(--el-color-primary);
            background: transparent;
            color: var(--el-color-primary);
            border-radius: 6px;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .action-btn:hover {
            background: var(--el-color-primary);
            color: white;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 平台标题 -->
        <div class="platform-header">
            <h1 class="platform-title">智慧"双反"平台</h1>
            <p class="platform-subtitle">案件详细信息管理系统</p>
        </div>

        <!-- 页签导航 -->
        <div class="tab-navigation">
            <div class="nav-tabs">
                <a class="nav-tab" href="/relations">
                    <i class="el-icon-connection"></i>
                    案件关系数据
                </a>
                <a class="nav-tab active" href="/case_details">
                    <i class="el-icon-document"></i>
                    案件详细信息列表
                </a>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-card">
            <!-- 搜索表单 -->
            <form class="search-form" method="GET">
                <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr auto auto; gap: 16px; align-items: end;">
                    <div>
                        <label style="display: block; margin-bottom: 8px; color: var(--el-text-color-regular); font-size: 14px;">关键词搜索</label>
                        <input type="text" class="search-input" name="search"
                               placeholder="搜索案件编号、案件名称、姓名..."
                               value="{{ search_term }}" style="width: 100%;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 8px; color: var(--el-text-color-regular); font-size: 14px;">数据批次号</label>
                        <input type="text" class="search-input" name="batchid"
                               placeholder="批次号" value="{{ batchid_filter }}" style="width: 100%;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 8px; color: var(--el-text-color-regular); font-size: 14px;">案件编号</label>
                        <input type="text" class="search-input" name="ajbh"
                               placeholder="案件编号" value="{{ ajbh_filter }}" style="width: 100%;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 8px; color: var(--el-text-color-regular); font-size: 14px;">每页显示</label>
                        <select class="search-select" name="per_page" style="width: 100%;">
                            <option value="20" {% if per_page == 20 %}selected{% endif %}>20条/页</option>
                            <option value="50" {% if per_page == 50 %}selected{% endif %}>50条/页</option>
                            <option value="100" {% if per_page == 100 %}selected{% endif %}>100条/页</option>
                        </select>
                    </div>
                    <div>
                        <button type="submit" class="search-btn">
                            🔍 搜索
                        </button>
                    </div>
                    <div>
                        <a href="/case_details" class="reset-btn">
                            🔄 重置
                        </a>
                    </div>
                </div>
            </form>

            <!-- 统计信息 -->
            <div class="stats-info">
                📊 数据统计：共找到 <strong>{{ total_records }}</strong> 条详细信息记录，当前第 <strong>{{ page }}</strong> 页，共 <strong>{{ total_pages }}</strong> 页
            </div>

            <!-- 数据表格 -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>记录ID</th>
                            <th>数据批次号</th>
                            <th>案件编号</th>
                            <th>案件名称</th>
                            <th>实体类型</th>
                            <th>姓名/代号/昵称/公司</th>
                            <th>性别</th>
                            <th>年龄</th>
                            <th>身份证号</th>
                            <th>户籍地/现居地</th>
                            <th>文化程度</th>
                            <th>直接上级</th>
                            <th>所属公司</th>
                            <th>所属组织</th>
                            <th>分工角色</th>
                            <th>主要职责</th>
                            <th>横向关联人物</th>
                            <th>横向关联关系</th>
                            <th>纵向关联人物</th>
                            <th>纵向关联关系</th>
                            <th>关联工具</th>
                            <th>关联物品</th>
                            <th>关联犯罪行为</th>
                            <th>关联场所</th>
                            <th>强制措施或状态</th>
                            <th>司法处置结果</th>
                            <th>经济收益（元）</th>
                            <th>前科</th>
                            <th>数据插入时间</th>
                            <th>删除状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for detail in details %}
                        <tr>
                            <td><span style="color: var(--el-color-primary); font-weight: 600;">{{ detail.id }}</span></td>
                            <td class="text-truncate" title="{{ detail.batchid }}">{{ detail.batchid }}</td>
                            <td><strong style="color: var(--el-color-primary);">{{ detail.ajbh }}</strong></td>
                            <td class="text-truncate" title="{{ detail.ajmc or '' }}">{{ detail.ajmc or '-' }}</td>
                            <td>
                                {% if detail.entity_type %}
                                <span class="status-badge" style="background: #f6ffed; color: #52c41a;">{{ detail.entity_type }}</span>
                                {% else %}-{% endif %}
                            </td>
                            <td><strong>{{ detail.name_code or '-' }}</strong></td>
                            <td>{{ detail.gender or '-' }}</td>
                            <td>{{ detail.age or '-' }}</td>
                            <td class="text-truncate" title="{{ detail.id_card or '' }}">{{ detail.id_card or '-' }}</td>
                            <td class="text-truncate" title="{{ detail.residence or '' }}">{{ detail.residence or '-' }}</td>
                            <td>{{ detail.education or '-' }}</td>
                            <td>{{ detail.direct_superior or '-' }}</td>
                            <td class="text-truncate" title="{{ detail.company or '' }}">{{ detail.company or '-' }}</td>
                            <td class="text-truncate" title="{{ detail.organization or '' }}">{{ detail.organization or '-' }}</td>
                            <td>{{ detail.role or '-' }}</td>
                            <td class="text-truncate" title="{{ detail.responsibilities or '' }}">{{ detail.responsibilities or '-' }}</td>
                            <td>{{ detail.peers_name or '-' }}</td>
                            <td class="text-truncate" title="{{ detail.peers or '' }}">{{ detail.peers or '-' }}</td>
                            <td>{{ detail.vertical_name or '-' }}</td>
                            <td class="text-truncate" title="{{ detail.vertical or '' }}">{{ detail.vertical or '-' }}</td>
                            <td class="text-truncate" title="{{ detail.related_tools or '' }}">{{ detail.related_tools or '-' }}</td>
                            <td class="text-truncate" title="{{ detail.related_items or '' }}">{{ detail.related_items or '-' }}</td>
                            <td class="text-truncate" title="{{ detail.related_actions or '' }}">
                                {% if detail.related_actions %}
                                    {{ detail.related_actions[:30] }}{% if detail.related_actions|length > 30 %}...{% endif %}
                                {% else %}-{% endif %}
                            </td>
                            <td class="text-truncate" title="{{ detail.related_locations or '' }}">
                                {% if detail.related_locations %}
                                    {{ detail.related_locations[:30] }}{% if detail.related_locations|length > 30 %}...{% endif %}
                                {% else %}-{% endif %}
                            </td>
                            <td>{{ detail.measures or '-' }}</td>
                            <td class="text-truncate" title="{{ detail.judicial_result or '' }}">{{ detail.judicial_result or '-' }}</td>
                            <td>{{ detail.economic or '-' }}</td>
                            <td>{{ detail.criminal or '-' }}</td>
                            <td class="text-truncate" title="{{ detail.data_time_formatted }}">{{ detail.data_time_formatted or '-' }}</td>
                            <td>
                                <span class="status-badge" style="background: {% if detail.isdelete_text == '有效' %}#f6ffed; color: #52c41a{% else %}#fff2f0; color: #ff4d4f{% endif %};">
                                    {{ detail.isdelete_text }}
                                </span>
                            </td>
                            <td>
                                <a href="/case_details/{{ detail.id }}" class="action-btn" title="查看详情">
                                    👁️ 详情
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if total_pages > 1 %}
            <div class="pagination">
                {% if page > 1 %}
                    <a class="page-btn" href="?page=1&per_page={{ per_page }}&search={{ search_term }}&batchid={{ batchid_filter }}&ajbh={{ ajbh_filter }}">⏮️ 首页</a>
                    <a class="page-btn" href="?page={{ page - 1 }}&per_page={{ per_page }}&search={{ search_term }}&batchid={{ batchid_filter }}&ajbh={{ ajbh_filter }}">⬅️ 上一页</a>
                {% endif %}

                {% for p in range(max(1, page - 2), min(total_pages + 1, page + 3)) %}
                    {% if p == page %}
                        <span class="page-btn active">{{ p }}</span>
                    {% else %}
                        <a class="page-btn" href="?page={{ p }}&per_page={{ per_page }}&search={{ search_term }}&batchid={{ batchid_filter }}&ajbh={{ ajbh_filter }}">{{ p }}</a>
                    {% endif %}
                {% endfor %}

                {% if page < total_pages %}
                    <a class="page-btn" href="?page={{ page + 1 }}&per_page={{ per_page }}&search={{ search_term }}&batchid={{ batchid_filter }}&ajbh={{ ajbh_filter }}">下一页 ➡️</a>
                    <a class="page-btn" href="?page={{ total_pages }}&per_page={{ per_page }}&search={{ search_term }}&batchid={{ batchid_filter }}&ajbh={{ ajbh_filter }}">末页 ⏭️</a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@3.3.8/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.full.js"></script>
    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 表格行悬停效果
            const rows = document.querySelectorAll('.data-table tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(2px)';
                    this.style.boxShadow = '0 2px 8px rgba(64, 158, 255, 0.15)';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                    this.style.boxShadow = 'none';
                });
            });

            // 搜索框焦点效果
            const searchInputs = document.querySelectorAll('.search-input, .search-select');
            searchInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.transform = 'translateY(-1px)';
                });
                input.addEventListener('blur', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
