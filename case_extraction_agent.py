#!/usr/bin/env python3
"""
案件要素提取和关系图智能体
从OCR识别结果获取案件内容，进行要素分析提取，生成关系图代码
支持异步处理，最大并发默认为10
"""

import asyncio
import logging
import json
import re
import pymysql
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
import time
import docker
import tempfile
import os
import uuid
import shutil

from autogen_agentchat.messages import TextMessage
from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken

from config import config


class CaseExtractionAgent:
    """案件要素提取智能体"""
    
    def __init__(self):
        self.db_config = config.get_db_config()
        self.model_client = config.get_model_client()
        self.system_config = config.get_system_config()
        self.table_config = config.get_table_config()
        self.logger = logging.getLogger(__name__)

        # 表名配置
        self.case_relation_table = self.table_config['case_relation_table']
        self.case_details_table = self.table_config['case_details_table']
        
        # 加载提示词模板
        self.system_prompt = self._load_system_prompt()
        self.logger.info(f"系统提示词加载完成，长度: {len(self.system_prompt)} 字符")
        
        # 要素提取字段
        self.extraction_fields = [
            "实体类型", "姓名/代号/昵称/公司", "性别", "年龄", "身份证号", "户籍地/现居地",
            "文化程度", "直接上级", "所属公司", "所属组织", "角色", "主要职责",
            "横向关联人物", "横向关联关系", "纵向关联人物", "纵向关联关系",
            "关联工具", "关联物品", "关联犯罪行为", "关联场所", "强制措施或状态",
            "司法处置结果", "经济收益", "前科"
        ]
        
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def _load_system_prompt(self) -> str:
        """加载系统提示词"""
        try:
            prompt_file = Path("案件要素提取关系图提示词.md")
            if prompt_file.exists():
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                return self._get_default_system_prompt()
        except Exception as e:
            self.logger.warning(f"加载提示词文件失败: {e}，使用默认提示词")
            return self._get_default_system_prompt()
    
    def _get_default_system_prompt(self) -> str:
        """获取默认系统提示词"""
        return """
你是一位专业的案件分析专家，专门负责分析案件内容，提取关键要素并生成可视化关系图谱。

请根据提供的案件内容，提取相关要素并生成Mermaid关系图代码。

要求：
1. 提取以下要素：实体类型,姓名/代号/昵称/公司,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,角色,主要职责,横向关联人物,横向关联关系,纵向关联人物,纵向关联关系,关联工具,关联物品,关联犯罪行为,关联场所,强制措施或状态,司法处置结果,经济收益,前科

2. 生成Mermaid关系图代码，展示人物、组织、行为之间的关系

3. 返回格式必须是有效的JSON，包含：
   - csv_data: 提取的要素数据（数组格式）
   - mermaid_code: Mermaid关系图代码

请确保返回的JSON格式正确，不要包含任何其他文本。
"""
    
    def get_cases_for_extraction(self, batch_id: str) -> List[Dict[str, Any]]:
        """
        获取需要进行要素提取的案件列表
        
        Args:
            batch_id: 批次号
            
        Returns:
            案件列表
        """
        try:
            sql = f"""
            SELECT batchid, ajbh, ajmc, ajnr, llsj, ajlx, tbrksj
            FROM `{self.case_relation_table}`
            WHERE batchid = %s AND status = '2' AND ajnr IS NOT NULL
            """
            
            with self.get_connection() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute(sql, (batch_id,))
                    cases = cursor.fetchall()
            
            self.logger.info(f"获取到 {len(cases)} 个需要要素提取的案件，批次: {batch_id}")
            return cases
            
        except Exception as e:
            self.logger.error(f"获取要素提取案件列表失败: {e}")
            return []
    
    async def extract_single_case(self, case_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取单个案件的要素信息
        
        Args:
            case_info: 案件信息
            
        Returns:
            提取结果
        """
        batch_id = case_info['batchid']
        ajbh = case_info['ajbh']
        ajmc = case_info['ajmc']
        ajnr = case_info['ajnr']
        
        try:
            self.logger.info(f"开始提取案件要素: {ajbh}")
            
            # 创建智能体实例
            agent_name = f"case_extractor_{ajbh}_{int(time.time())}"
            case_agent = AssistantAgent(
                name=agent_name,
                model_client=self.model_client,
                system_message=self.system_prompt
            )
            
            # 构建提示词
            prompt = f"""
案件编号：{ajbh}
案件名称：{ajmc}
批次号：{batch_id}

案件内容：
{ajnr}

请严格按照系统提示词的要求进行分析，完成以下任务：

1. 深度分析案件内容，识别所有嫌疑人员（包括真实姓名、代号、昵称等）
2. 严格按照24列CSV格式提取要素数据
3. 生成符合要求的Mermaid关系图代码

必须返回以下JSON格式：
{{
    "analysis": "详细的组织架构和供应链分析",
    "csv_data": [
        {{
            "实体类型": "人员",
            "姓名/代号/昵称/公司": "张某某",
            "性别": "男",
            "年龄": "35",
            "身份证号": "",
            "户籍地/现居地": "",
            "文化程度": "",
            "直接上级": "",
            "所属公司": "",
            "所属组织": "",
            "角色": "组织者",
            "主要职责": "负责整体规划",
            "横向关联人物": "",
            "横向关联关系": "",
            "纵向关联人物": "",
            "纵向关联关系": "",
            "关联工具": "",
            "关联物品": "",
            "关联犯罪行为": "",
            "关联场所": "",
            "强制措施或状态": "",
            "司法处置结果": "",
            "经济收益": "",
            "前科": ""
        }}
    ],
    "mermaid_code": "完整的Mermaid关系图代码"
}}

⚠️ 重要要求：
- 严格遵循系统提示词中的所有规则和要求
- 绝对不推测案件中未明确提及的信息
- 确保所有嫌疑人员都被提取，不得遗漏
- Mermaid代码必须符合语法要求，避免个体重复连线
- 返回的必须是有效的JSON格式，不要包含任何其他文本
"""
            
            # 发送请求
            response = await case_agent.on_messages(
                [TextMessage(content=prompt, source="user")],
                CancellationToken()
            )
            
            model_response = response.chat_message.content
            self.logger.info(f"智能体响应成功: {ajbh}, 响应长度: {len(model_response)}")
            
            # 解析JSON响应
            try:
                # 预处理响应
                cleaned_response = self._preprocess_response(model_response)
                result = json.loads(cleaned_response)
                
                # 验证结果格式
                if not isinstance(result, dict):
                    raise ValueError("响应不是有效的JSON对象")

                # 检查必要字段
                required_fields = ['csv_data', 'mermaid_code']
                missing_fields = [field for field in required_fields if field not in result]
                if missing_fields:
                    raise ValueError(f"响应格式不正确，缺少必要字段: {', '.join(missing_fields)}")

                # 验证csv_data格式
                if not isinstance(result['csv_data'], list):
                    raise ValueError("csv_data必须是数组格式")

                # 验证mermaid_code格式
                if not isinstance(result['mermaid_code'], str) or not result['mermaid_code'].strip():
                    raise ValueError("mermaid_code必须是非空字符串")
                
                self.logger.info(f"案件要素提取成功: {ajbh}")
                
                return {
                    "status": "success",
                    "batch_id": batch_id,
                    "ajbh": ajbh,
                    "ajmc": ajmc,
                    "csv_data": result['csv_data'],
                    "mermaid_code": result['mermaid_code'],
                    "raw_response": model_response,
                    "nums": 0
                }
                
            except (json.JSONDecodeError, ValueError) as e:
                self.logger.warning(f"JSON解析失败: {ajbh} - {e}")
                
                # 尝试修复
                repair_result = await self._try_repair_response(
                    model_response, batch_id, ajbh, ajmc, ajnr, 1
                )
                
                return repair_result
                    
        except Exception as e:
            self.logger.error(f"案件要素提取失败: {ajbh} - {e}")
            return {
                "status": "error",
                "batch_id": batch_id,
                "ajbh": ajbh,
                "ajmc": ajmc,
                "error": str(e),
                "nums": 0
            }
    
    def _preprocess_response(self, response: str) -> str:
        """预处理响应，移除非JSON内容"""
        try:
            # 移除思考标签
            response = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL)
            
            # 提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json_match.group(0)
            
            return response.strip()
            
        except Exception as e:
            self.logger.warning(f"预处理响应失败: {e}")
            return response
    
    async def _try_repair_response(self, failed_response: str, batch_id: str, ajbh: str, 
                                  ajmc: str, ajnr: str, attempt: int) -> Dict[str, Any]:
        """尝试修复失败的响应"""
        try:
            if attempt > self.system_config['max_repair_attempts']:
                self.logger.error(f"修复次数超限: {ajbh}, 尝试次数: {attempt}")
                return {
                    "status": "error",
                    "batch_id": batch_id,
                    "ajbh": ajbh,
                    "error": f"修复失败，超过最大尝试次数: {attempt}",
                    "nums": attempt
                }
            
            self.logger.info(f"尝试修复响应: {ajbh}, 第{attempt}次")
            
            repair_prompt = f"""
之前的响应解析失败，请重新分析以下案件并返回正确的JSON格式：

案件编号：{ajbh}
案件名称：{ajmc}
批次号：{batch_id}

案件内容：
{ajnr}

失败的响应：
{failed_response[:1000]}...

请确保返回有效的JSON格式，包含csv_data和mermaid_code字段。
要素字段：{', '.join(self.extraction_fields)}
"""
            
            # 创建修复智能体
            repair_agent = AssistantAgent(
                name=f"repair_agent_{ajbh}_{int(time.time())}",
                model_client=self.model_client,
                system_message=self.system_prompt
            )
            
            response = await repair_agent.on_messages(
                [TextMessage(content=repair_prompt, source="user")],
                CancellationToken()
            )
            
            model_response = response.chat_message.content
            
            # 解析修复后的响应
            try:
                cleaned_response = self._preprocess_response(model_response)
                result = json.loads(cleaned_response)
                
                if not isinstance(result, dict) or 'csv_data' not in result or 'mermaid_code' not in result:
                    raise ValueError("修复后的响应格式仍然不正确")
                
                self.logger.info(f"响应修复成功: {ajbh}, 第{attempt}次")
                
                return {
                    "status": "success",
                    "batch_id": batch_id,
                    "ajbh": ajbh,
                    "ajmc": ajmc,
                    "csv_data": result['csv_data'],
                    "mermaid_code": result['mermaid_code'],
                    "raw_response": model_response,
                    "repaired": True,
                    "nums": attempt
                }
                
            except (json.JSONDecodeError, ValueError) as e:
                self.logger.warning(f"修复后仍然解析失败: {ajbh}, 第{attempt}次 - {e}")
                # 递归尝试下一次修复
                return await self._try_repair_response(
                    model_response, batch_id, ajbh, ajmc, ajnr, attempt + 1
                )
            
        except Exception as e:
            self.logger.error(f"响应修复异常: {ajbh}, 第{attempt}次 - {e}")
            return {
                "status": "error",
                "batch_id": batch_id,
                "ajbh": ajbh,
                "error": f"修复异常: {str(e)}",
                "nums": attempt
            }


    def validate_mermaid_code(self, mermaid_code: str) -> Dict[str, Any]:
        """
        使用Docker生成Mermaid关系图验证代码是否能正常画图
        优化版本：支持快速语法检查 + Docker验证

        Args:
            mermaid_code: Mermaid代码

        Returns:
            验证结果
        """
        try:
            # 第一步：快速语法检查
            syntax_check = self._quick_syntax_check(mermaid_code)
            if not syntax_check["valid"]:
                return syntax_check

            # 第二步：根据配置选择验证方法
            validation_method = self.system_config.get('mermaid_validation_method', 'docker')

            if validation_method == 'docker':
                return self._docker_render_validation(mermaid_code)
            elif validation_method == 'live_editor':
                return self._live_editor_api_validation(mermaid_code)
            elif validation_method == 'hybrid':
                # 先尝试Live Editor，失败则使用Docker
                live_result = self._live_editor_api_validation(mermaid_code)
                if live_result["valid"]:
                    return live_result
                else:
                    self.logger.info("Live Editor验证失败，尝试Docker验证")
                    return self._docker_render_validation(mermaid_code)
            else:
                return self._docker_render_validation(mermaid_code)

        except Exception as e:
            self.logger.error(f"Mermaid代码验证失败: {e}")
            return {
                "valid": False,
                "error": f"验证异常: {str(e)}"
            }

    def _quick_syntax_check(self, mermaid_code: str) -> Dict[str, Any]:
        """
        快速语法检查，过滤明显的语法错误

        Args:
            mermaid_code: Mermaid代码

        Returns:
            检查结果
        """
        try:
            if not mermaid_code or not mermaid_code.strip():
                return {
                    "valid": False,
                    "error": "Mermaid代码为空"
                }

            lines = mermaid_code.strip().split('\n')

            # 检查是否有图表类型声明
            has_graph_declaration = False
            for line in lines:
                line = line.strip()
                if line.startswith(('graph', 'flowchart', 'sequenceDiagram', 'classDiagram')):
                    has_graph_declaration = True
                    break

            if not has_graph_declaration:
                return {
                    "valid": False,
                    "error": "缺少图表类型声明（如 graph TD, flowchart LR 等）"
                }

            # 检查基本语法错误
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('%%'):  # 空行或注释
                    continue

                # 检查是否有未闭合的引号
                if line.count('"') % 2 != 0:
                    return {
                        "valid": False,
                        "error": f"第{i}行：引号未正确闭合"
                    }

                # 检查是否有未闭合的方括号
                if line.count('[') != line.count(']'):
                    return {
                        "valid": False,
                        "error": f"第{i}行：方括号未正确闭合"
                    }

            return {
                "valid": True,
                "message": "快速语法检查通过"
            }

        except Exception as e:
            return {
                "valid": False,
                "error": f"语法检查异常: {str(e)}"
            }

    def _docker_render_validation(self, mermaid_code: str) -> Dict[str, Any]:
        """
        使用Docker进行实际渲染验证
        参考原有agents.py的实现，解决权限问题

        Args:
            mermaid_code: Mermaid代码

        Returns:
            验证结果
        """
        work_dir = None
        mmd_file = None
        png_file = None

        try:
            # 创建专门的工作目录（而不是使用临时文件）
            import uuid
            work_dir_name = f"mermaid_validation_{uuid.uuid4().hex[:8]}"
            work_dir = Path("/tmp") / work_dir_name
            work_dir.mkdir(parents=True, exist_ok=True)

            # 设置目录权限
            os.chmod(work_dir, 0o755)

            # 创建文件路径
            mmd_file = work_dir / "validation.mmd"
            png_file = work_dir / "validation.png"

            # 写入Mermaid代码文件
            with open(mmd_file, 'w', encoding='utf-8') as f:
                f.write(mermaid_code)

            # 设置文件权限（参考原有实现）
            os.chmod(mmd_file, 0o644)

            # 验证文件是否创建成功
            if not mmd_file.exists():
                return {
                    "valid": False,
                    "error": "Mermaid文件创建失败"
                }

            # 验证文件内容
            with open(mmd_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    return {
                        "valid": False,
                        "error": "Mermaid文件内容为空"
                    }

            self.logger.info(f"Mermaid验证文件创建成功: {mmd_file}, 内容长度: {len(content)}")

            # 使用Docker生成图片（参考原有实现）
            client = docker.from_env()

            # 检查并拉取镜像
            docker_image = self.system_config.get('mermaid_docker_image', 'minlag/mermaid-cli:latest')
            try:
                client.images.get(docker_image)
            except docker.errors.ImageNotFound:
                self.logger.info(f"拉取Mermaid镜像: {docker_image}")
                client.images.pull(docker_image)

            # 运行容器生成图像（使用原有的成功配置）
            container_result = client.containers.run(
                docker_image,
                command=[
                    'mmdc',
                    '-i', '/data/validation.mmd',
                    '-o', '/data/validation.png',
                    '--theme', 'default',
                    '--scale', '1',  # 验证时使用较小缩放
                    '--backgroundColor', 'white'
                ],
                volumes={
                    str(work_dir.resolve()): {'bind': '/data', 'mode': 'rw'}  # 使用绝对路径
                },
                working_dir='/data',
                user='root',  # 确保有足够权限（关键！）
                remove=True,
                detach=False,
                stdout=True,
                stderr=True
            )

            self.logger.info(f"Docker容器执行完成")

            # 等待文件生成
            import time
            time.sleep(0.5)

            # 检查输出文件是否生成
            if png_file.exists() and png_file.stat().st_size > 0:
                self.logger.info("Mermaid代码Docker验证通过")
                return {
                    "valid": True,
                    "message": "Mermaid代码可以正常生成图片"
                }
            else:
                return {
                    "valid": False,
                    "error": "生成的图片文件为空或不存在"
                }

        except docker.errors.ContainerError as e:
            # 容器执行错误，通常是Mermaid语法错误
            error_msg = "Mermaid语法错误"
            if e.stderr:
                stderr_output = e.stderr.decode() if isinstance(e.stderr, bytes) else str(e.stderr)
                error_msg += f": {stderr_output}"

            self.logger.warning(f"Mermaid代码验证失败: {error_msg}")
            return {
                "valid": False,
                "error": error_msg
            }

        except Exception as e:
            self.logger.error(f"Docker验证异常: {e}")
            return {
                "valid": False,
                "error": f"Docker验证失败: {str(e)}"
            }

        finally:
            # 清理工作目录
            try:
                if work_dir and work_dir.exists():
                    import shutil
                    shutil.rmtree(work_dir, ignore_errors=True)
            except Exception as e:
                self.logger.warning(f"清理工作目录失败: {e}")

    def _live_editor_api_validation(self, mermaid_code: str) -> Dict[str, Any]:
        """
        使用本地Mermaid Live Editor API进行验证

        Args:
            mermaid_code: Mermaid代码

        Returns:
            验证结果
        """
        try:
            import requests
            import json
            import base64

            # 获取Live Editor URL
            live_editor_url = self.system_config.get('mermaid_live_editor_url', 'http://10.10.20.42:8008')

            # 准备验证请求
            validation_payload = {
                "code": mermaid_code,
                "mermaid": {"theme": "default"},
                "autoSync": True,
                "updateDiagram": False
            }

            # 发送验证请求到本地Live Editor
            timeout = self.system_config.get('docker_validation_timeout', 10)

            # 方法1：尝试直接验证API
            try:
                response = requests.post(
                    f"{live_editor_url}/api/validate",
                    json=validation_payload,
                    timeout=timeout,
                    headers={'Content-Type': 'application/json'}
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("valid", False):
                        return {
                            "valid": True,
                            "message": "Live Editor API验证通过"
                        }
                    else:
                        self.logger.warning(f"Live Editor验证失败: {result.get('error', 'Unknown error')}")
                elif response.status_code == 405:
                    self.logger.warning("Live Editor API方法不支持，尝试其他方式")
                else:
                    self.logger.warning(f"Live Editor API返回错误: {response.status_code}")

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Live Editor API请求异常: {e}")
                # API不可用，尝试其他方法
                pass

            # 方法2：尝试GET方式的编码验证（Mermaid Live常用方式）
            try:
                import urllib.parse
                # 将Mermaid代码编码为base64
                encoded_code = base64.b64encode(mermaid_code.encode('utf-8')).decode('utf-8')

                # 构建验证URL
                validation_url = f"{live_editor_url}/svg/{encoded_code}"

                response = requests.get(
                    validation_url,
                    timeout=timeout,
                    headers={'Accept': 'image/svg+xml'}
                )

                if response.status_code == 200:
                    # 检查返回的SVG是否有效
                    svg_content = response.text
                    if svg_content and "<svg" in svg_content and "</svg>" in svg_content:
                        self.logger.info("Live Editor GET方式验证通过")
                        return {
                            "valid": True,
                            "message": "Live Editor GET验证通过"
                        }
                    else:
                        return {
                            "valid": False,
                            "error": "Live Editor返回的SVG无效"
                        }
                elif response.status_code == 400:
                    return {
                        "valid": False,
                        "error": "Mermaid语法错误"
                    }

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Live Editor GET方式失败: {e}")

            # 方法3：尝试POST表单方式
            try:
                response = requests.post(
                    f"{live_editor_url}/",
                    data={
                        "code": mermaid_code,
                        "format": "svg"
                    },
                    timeout=timeout,
                    headers={'Content-Type': 'application/x-www-form-urlencoded'}
                )

                if response.status_code == 200:
                    svg_content = response.text
                    if svg_content and "<svg" in svg_content and "</svg>" in svg_content:
                        self.logger.info("Live Editor POST表单方式验证通过")
                        return {
                            "valid": True,
                            "message": "Live Editor POST验证通过"
                        }
                elif response.status_code == 400:
                    return {
                        "valid": False,
                        "error": "Mermaid语法错误"
                    }

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Live Editor POST表单方式失败: {e}")

            # 如果所有API都失败，回退到Docker验证
            self.logger.warning("所有Live Editor API方式都失败，回退到Docker验证")
            return self._docker_render_validation(mermaid_code)

        except Exception as e:
            self.logger.error(f"Live Editor API验证异常: {e}")
            # 异常时也回退到Docker验证
            self.logger.info("Live Editor异常，回退到Docker验证")
            return self._docker_render_validation(mermaid_code)

    def insert_case_details(self, batch_id: str, ajbh: str, ajmc: str,
                           csv_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        插入案件详细信息到ds_case_details表

        Args:
            batch_id: 批次号
            ajbh: 案件编号
            ajmc: 案件名称
            csv_data: CSV数据列表

        Returns:
            插入结果
        """
        try:
            if not csv_data:
                return {
                    "status": "error",
                    "message": "没有CSV数据需要插入"
                }

            # 准备插入数据
            insert_data = []
            current_time = datetime.now()

            for row in csv_data:
                # 构建插入数据
                record = (
                    batch_id,
                    ajbh,
                    ajmc,
                    row.get('实体类型', ''),
                    row.get('姓名/代号/昵称/公司', ''),
                    row.get('性别', ''),
                    row.get('年龄', ''),
                    row.get('身份证号', ''),
                    row.get('户籍地/现居地', ''),
                    row.get('文化程度', ''),
                    row.get('直接上级', ''),
                    row.get('所属公司', ''),
                    row.get('所属组织', ''),
                    row.get('角色', ''),
                    row.get('主要职责', ''),
                    row.get('横向关联人物', ''),
                    row.get('横向关联关系', ''),
                    row.get('纵向关联人物', ''),
                    row.get('纵向关联关系', ''),
                    row.get('关联工具', ''),
                    row.get('关联物品', ''),
                    row.get('关联犯罪行为', ''),
                    row.get('关联场所', ''),
                    row.get('强制措施或状态', ''),
                    row.get('司法处置结果', ''),
                    row.get('经济收益', ''),
                    row.get('前科', ''),
                    current_time
                )
                insert_data.append(record)

            with self.get_connection() as connection:
                with connection.cursor() as cursor:

                    # 1. 先删除该批次该案件的旧数据（如果存在）
                    delete_sql = f"""
                    DELETE FROM `{self.case_details_table}`
                    WHERE batchid = %s AND ajbh = %s
                    """
                    cursor.execute(delete_sql, (batch_id, ajbh))
                    deleted_rows = cursor.rowcount

                    if deleted_rows > 0:
                        self.logger.info(f"删除案件 {ajbh} 的 {deleted_rows} 条旧记录")

                    # 2. 执行插入新数据
                    insert_sql = f"""
                    INSERT INTO `{self.case_details_table}`
                    (batchid, ajbh, ajmc, entity_type, name_code, gender, age, id_card,
                     residence, education, direct_superior, company, organization, role,
                     responsibilities, peers_name, peers, vertical_name, vertical,
                     related_tools, related_items, related_actions, related_locations,
                     measures, judicial_result, economic, criminal, data_time)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """

                    affected_rows = cursor.executemany(insert_sql, insert_data)
                    connection.commit()

            self.logger.info(f"成功插入 {len(insert_data)} 条案件详细信息，案件: {ajbh}")

            return {
                "status": "success",
                "message": f"成功插入 {len(insert_data)} 条记录",
                "affected_rows": affected_rows,
                "ajbh": ajbh
            }

        except Exception as e:
            self.logger.error(f"插入案件详细信息失败: {ajbh} - {e}")
            return {
                "status": "error",
                "error": str(e),
                "ajbh": ajbh
            }

    def update_case_relation_success(self, batch_id: str, ajbh: str,
                                   mermaid_code: str, nums: int = 0) -> Dict[str, Any]:
        """
        更新案件关系表为成功状态

        Args:
            batch_id: 批次号
            ajbh: 案件编号
            mermaid_code: Mermaid代码
            nums: AI重跑次数

        Returns:
            更新结果
        """
        try:
            update_sql = f"""
            UPDATE `{self.case_relation_table}`
            SET code = %s, lastcode = %s, status = '3', endtime = NOW(),
                updatetime = NOW(), nums = %s
            WHERE batchid = %s AND ajbh = %s
            """

            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    affected_rows = cursor.execute(update_sql,
                                                 (mermaid_code, mermaid_code, nums, batch_id, ajbh))
                    connection.commit()

            if affected_rows > 0:
                self.logger.info(f"成功更新案件关系代码: {ajbh}")
                return {
                    "status": "success",
                    "message": "案件处理完成",
                    "ajbh": ajbh,
                    "affected_rows": affected_rows
                }
            else:
                return {
                    "status": "error",
                    "error": "未找到对应的案件记录",
                    "ajbh": ajbh
                }

        except Exception as e:
            self.logger.error(f"更新案件关系代码失败: {ajbh} - {e}")
            return {
                "status": "error",
                "error": str(e),
                "ajbh": ajbh
            }

    def update_case_relation_error(self, batch_id: str, ajbh: str,
                                 error_message: str, nums: int = 0) -> Dict[str, Any]:
        """
        更新案件关系表为错误状态

        Args:
            batch_id: 批次号
            ajbh: 案件编号
            error_message: 错误信息
            nums: AI重跑次数

        Returns:
            更新结果
        """
        try:
            update_sql = f"""
            UPDATE `{self.case_relation_table}`
            SET status = '4', error = %s, updatetime = NOW(), nums = %s
            WHERE batchid = %s AND ajbh = %s
            """

            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    affected_rows = cursor.execute(update_sql,
                                                 (error_message, nums, batch_id, ajbh))
                    connection.commit()

            self.logger.info(f"更新案件错误状态: {ajbh} - {error_message}")

            return {
                "status": "success",
                "message": "错误状态更新成功",
                "ajbh": ajbh,
                "affected_rows": affected_rows
            }

        except Exception as e:
            self.logger.error(f"更新案件错误状态失败: {ajbh} - {e}")
            return {
                "status": "error",
                "error": str(e),
                "ajbh": ajbh
            }


    async def process_batch_extraction(self, batch_id: str) -> Dict[str, Any]:
        """
        处理整个批次的案件要素提取

        Args:
            batch_id: 批次号

        Returns:
            批次处理结果
        """
        try:
            self.logger.info(f"开始批次要素提取: {batch_id}")

            # 获取需要处理的案件
            cases = self.get_cases_for_extraction(batch_id)

            if not cases:
                return {
                    "status": "success",
                    "batch_id": batch_id,
                    "message": "没有需要处理的案件",
                    "total_count": 0,
                    "successful_count": 0,
                    "failed_count": 0
                }

            # 创建并发任务
            tasks = []
            for case_info in cases:
                task = self.process_single_case_complete(case_info)
                tasks.append(task)

            # 控制并发数
            semaphore = asyncio.Semaphore(self.system_config['max_concurrent'])

            async def process_with_semaphore(task):
                async with semaphore:
                    return await task

            # 执行并发任务
            results = await asyncio.gather(
                *[process_with_semaphore(task) for task in tasks],
                return_exceptions=True
            )

            # 统计结果
            successful_cases = []
            failed_cases = []

            for result in results:
                if isinstance(result, Exception):
                    failed_cases.append({
                        "ajbh": "unknown",
                        "error": str(result)
                    })
                elif result.get("status") == "success":
                    successful_cases.append(result)
                else:
                    failed_cases.append(result)

            self.logger.info(f"批次要素提取完成: {batch_id} - 成功: {len(successful_cases)}, 失败: {len(failed_cases)}")

            return {
                "status": "success",
                "batch_id": batch_id,
                "total_count": len(cases),
                "successful_count": len(successful_cases),
                "failed_count": len(failed_cases),
                "successful_cases": successful_cases,
                "failed_cases": failed_cases,
                "summary": f"批次 {batch_id} 处理完成，成功 {len(successful_cases)}/{len(cases)} 个案件"
            }

        except Exception as e:
            self.logger.error(f"批次要素提取失败: {batch_id} - {e}")
            return {
                "status": "error",
                "batch_id": batch_id,
                "error": str(e)
            }

    async def process_single_case_complete(self, case_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        完整处理单个案件（包括要素提取、数据入库、关系图验证）

        Args:
            case_info: 案件信息

        Returns:
            处理结果
        """
        batch_id = case_info['batchid']
        ajbh = case_info['ajbh']
        ajmc = case_info['ajmc']

        try:
            self.logger.info(f"开始完整处理案件: {ajbh}")

            # 1. 提取案件要素
            extraction_result = await self.extract_single_case(case_info)

            if extraction_result["status"] != "success":
                # 提取失败，更新错误状态
                error_msg = extraction_result.get("error", "要素提取失败")
                nums = extraction_result.get("nums", 0)
                self.update_case_relation_error(batch_id, ajbh, error_msg, nums)
                return extraction_result

            # 2. 插入CSV数据到案件详细信息表
            csv_result = self.insert_case_details(
                batch_id, ajbh, ajmc, extraction_result["csv_data"]
            )

            if csv_result["status"] != "success":
                error_msg = f"CSV数据插入失败: {csv_result.get('error', '未知错误')}"
                nums = extraction_result.get("nums", 0)
                self.update_case_relation_error(batch_id, ajbh, error_msg, nums)
                return {
                    "status": "error",
                    "batch_id": batch_id,
                    "ajbh": ajbh,
                    "error": error_msg,
                    "nums": nums
                }

            # 3. 验证Mermaid代码
            mermaid_code = extraction_result["mermaid_code"]
            validation_result = self.validate_mermaid_code(mermaid_code)

            if validation_result["valid"]:
                # 验证通过，更新成功状态
                nums = extraction_result.get("nums", 0)
                update_result = self.update_case_relation_success(
                    batch_id, ajbh, mermaid_code, nums
                )

                self.logger.info(f"案件完整处理成功: {ajbh}")

                return {
                    "status": "success",
                    "batch_id": batch_id,
                    "ajbh": ajbh,
                    "ajmc": ajmc,
                    "extraction_result": extraction_result,
                    "csv_result": csv_result,
                    "validation_result": validation_result,
                    "update_result": update_result,
                    "nums": nums,
                    "message": "案件处理完成"
                }
            else:
                # Mermaid验证失败，尝试修复
                self.logger.warning(f"Mermaid验证失败: {ajbh} - {validation_result.get('error')}")
                self.logger.info(f"开始调用修复智能体: {ajbh}")

                # 尝试修复Mermaid代码
                repair_result = await self._try_repair_mermaid(
                    mermaid_code, batch_id, ajbh, ajmc, case_info['ajnr'], 1
                )

                self.logger.info(f"修复智能体完成: {ajbh}, 结果: {repair_result.get('status')}")
                return repair_result

        except Exception as e:
            self.logger.error(f"案件完整处理异常: {ajbh} - {e}")

            # 更新错误状态
            self.update_case_relation_error(batch_id, ajbh, str(e))

            return {
                "status": "error",
                "batch_id": batch_id,
                "ajbh": ajbh,
                "error": str(e)
            }

    async def _try_repair_mermaid(self, failed_mermaid: str, batch_id: str, ajbh: str,
                                 ajmc: str, ajnr: str, attempt: int) -> Dict[str, Any]:
        """尝试修复失败的Mermaid代码"""
        try:
            if attempt > self.system_config['max_repair_attempts']:
                self.logger.error(f"Mermaid修复次数超限: {ajbh}, 尝试次数: {attempt}")
                error_msg = f"Mermaid代码修复失败，超过最大尝试次数: {attempt}"
                self.update_case_relation_error(batch_id, ajbh, error_msg, attempt)
                return {
                    "status": "error",
                    "batch_id": batch_id,
                    "ajbh": ajbh,
                    "error": error_msg,
                    "nums": attempt
                }

            self.logger.info(f"尝试修复Mermaid代码: {ajbh}, 第{attempt}次")

            repair_prompt = f"""
之前生成的Mermaid关系图代码验证失败，请重新分析案件并生成正确的代码：

案件编号：{ajbh}
案件名称：{ajmc}
批次号：{batch_id}

案件内容：
{ajnr}

失败的Mermaid代码：
{failed_mermaid[:1000]}...

请严格按照系统提示词的要求重新生成，特别注意：
1. 遵循分组边界连线规则，避免个体重复连线
2. 确保Mermaid语法正确，可以正常渲染
3. 节点内容避免使用危险字符如括号()、引号等
4. 保持图表简洁清晰

必须返回以下JSON格式：
{{
    "mermaid_code": "修复后的完整Mermaid关系图代码"
}}

⚠️ 重要：只返回JSON格式，不要包含任何其他文本。
"""

            # 创建修复智能体
            repair_agent = AssistantAgent(
                name=f"mermaid_repair_agent_{ajbh}_{int(time.time())}",
                model_client=self.model_client,
                system_message=self.system_prompt
            )

            response = await repair_agent.on_messages(
                [TextMessage(content=repair_prompt, source="user")],
                CancellationToken()
            )

            model_response = response.chat_message.content

            # 解析修复后的响应
            try:
                cleaned_response = self._preprocess_response(model_response)
                result = json.loads(cleaned_response)

                if not isinstance(result, dict) or 'mermaid_code' not in result:
                    raise ValueError("修复后的响应格式仍然不正确")

                # 验证修复后的Mermaid代码
                repaired_mermaid = result['mermaid_code']
                validation_result = self.validate_mermaid_code(repaired_mermaid)

                if validation_result["valid"]:
                    # 修复成功，更新数据库
                    update_result = self.update_case_relation_success(
                        batch_id, ajbh, repaired_mermaid, attempt
                    )

                    self.logger.info(f"Mermaid代码修复成功: {ajbh}, 第{attempt}次")

                    return {
                        "status": "success",
                        "batch_id": batch_id,
                        "ajbh": ajbh,
                        "ajmc": ajmc,
                        "mermaid_code": repaired_mermaid,
                        "validation_result": validation_result,
                        "update_result": update_result,
                        "repaired": True,
                        "nums": attempt,
                        "message": f"Mermaid代码修复成功，第{attempt}次尝试"
                    }
                else:
                    # 修复后仍然验证失败，递归尝试下一次修复
                    self.logger.warning(f"修复后仍然验证失败: {ajbh}, 第{attempt}次 - {validation_result.get('error')}")
                    return await self._try_repair_mermaid(
                        repaired_mermaid, batch_id, ajbh, ajmc, ajnr, attempt + 1
                    )

            except (json.JSONDecodeError, ValueError) as e:
                self.logger.warning(f"修复后仍然解析失败: {ajbh}, 第{attempt}次 - {e}")
                # 递归尝试下一次修复
                return await self._try_repair_mermaid(
                    model_response, batch_id, ajbh, ajmc, ajnr, attempt + 1
                )

        except Exception as e:
            self.logger.error(f"Mermaid修复异常: {ajbh}, 第{attempt}次 - {e}")
            error_msg = f"Mermaid修复异常: {str(e)}"
            self.update_case_relation_error(batch_id, ajbh, error_msg, attempt)
            return {
                "status": "error",
                "batch_id": batch_id,
                "ajbh": ajbh,
                "error": error_msg,
                "nums": attempt
            }


def main():
    """测试函数"""
    logging.basicConfig(level=logging.INFO)

    agent = CaseExtractionAgent()

    # 测试批次
    batch_id = "2025073009461901"

    # 获取案件列表
    cases = agent.get_cases_for_extraction(batch_id)
    print(f"获取到 {len(cases)} 个案件")

    # 测试批次处理
    if cases:
        result = asyncio.run(agent.process_batch_extraction(batch_id))
        print(f"批次处理结果: {result}")


if __name__ == "__main__":
    main()
