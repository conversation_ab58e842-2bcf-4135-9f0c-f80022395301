#!/usr/bin/env python3
"""
批量替换表名的脚本
将硬编码的表名替换为配置化的表名
"""

import os
import re

def replace_table_names_in_file(file_path, replacements):
    """在文件中替换表名"""
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 执行替换
        for old_pattern, new_pattern in replacements.items():
            content = re.sub(old_pattern, new_pattern, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已更新: {file_path}")
            return True
        else:
            print(f"⏸️  无需更新: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 处理文件失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    
    print("🔄 批量替换表名为配置化表名")
    print("="*50)
    
    # 定义替换规则
    web_case_viewer_replacements = {
        r'FROM ds_case_relation': f'FROM {{get_table_name("case_relation_table")}}',
        r'UPDATE ds_case_relation': f'UPDATE {{get_table_name("case_relation_table")}}',
        r'INSERT INTO ds_case_relation': f'INSERT INTO {{get_table_name("case_relation_table")}}',
        r'FROM `ds_case_relation`': f'FROM `{{get_table_name("case_relation_table")}}`',
        r'UPDATE `ds_case_relation`': f'UPDATE `{{get_table_name("case_relation_table")}}`',
        r'INSERT INTO `ds_case_relation`': f'INSERT INTO `{{get_table_name("case_relation_table")}}`',
    }
    
    # 其他文件的替换规则
    other_files_replacements = {
        r'ds_case_instrument_his_ai': 'config.get_table_name("source_table")',
        r'ds_case_relation': 'config.get_table_name("case_relation_table")',
        r'ds_case_details': 'config.get_table_name("case_details_table")',
    }
    
    # 需要处理的文件列表
    files_to_process = [
        ('html_main/web_case_viewer.py', web_case_viewer_replacements),
        ('html_main/diagnose_database.py', other_files_replacements),
        ('ocr_processor.py', other_files_replacements),
        ('case_extraction_agent.py', other_files_replacements),
    ]
    
    updated_files = []
    
    for file_path, replacements in files_to_process:
        if replace_table_names_in_file(file_path, replacements):
            updated_files.append(file_path)
    
    print(f"\n📊 处理结果:")
    print(f"已更新文件数: {len(updated_files)}")
    
    if updated_files:
        print(f"\n已更新的文件:")
        for file_path in updated_files:
            print(f"  - {file_path}")
    
    print(f"\n⚠️  注意事项:")
    print(f"1. 请检查更新后的文件语法是否正确")
    print(f"2. 某些复杂的SQL语句可能需要手动调整")
    print(f"3. 建议运行测试确保功能正常")

if __name__ == "__main__":
    main()
